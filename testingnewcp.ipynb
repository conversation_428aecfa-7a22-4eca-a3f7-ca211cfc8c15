{"cells": [{"cell_type": "code", "execution_count": 1, "id": "68dcab69", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["auto.py (21): IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n"]}], "source": ["import importlib\n", "import ifimage_tools\n", "importlib.reload(ifimage_tools)\n", "import pandas as pd\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "from preprocessing import SamplePreprocessor,batch_process_zip_rois,batch_rename,mask_to_rois_zip"]}, {"cell_type": "code", "execution_count": 4, "id": "ceed84c7", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "image_dir = \"Reorgnized Ground Truth\"\n", "masks_dir = \"Reorgnized Ground Truth/mask\"\n", "dataset = ifimage_tools.IfImageDataset(image_dir, masks_dir, {})\n", "dataset.load_data()\n", "\n", "old_version_sample_ids = [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]\n", "for sample_id in old_version_sample_ids:\n", "    if sample_id in dataset.samples:\n", "        del dataset.samples[sample_id]"]}, {"cell_type": "code", "execution_count": null, "id": "3db344d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["—— 一共发现 60 个样本，需要先跑 apply_nuc_pipeline，然后生成 ROI：['8550', '8917', '5191', '6833', '10166', '9106', '4642', '5792', '10061', '1110', '7925', '5863', '1112', '7739', '5059', '7113', '3569', '7685', '8224', '4515', '7870', '6790', '7962', '1111', '8746', '3532', '3999', '3527', '7071', '5923', '5789', '5794', '9755', '6020', '9170', '10015', '1116', '4238', '1114', '5795', '4548', '8517', '9783', '1108', '8310', '9472', '4736', '7319', '9866', '6212', '8942', '1113', '6748', '1115', '5410', '6523', '6466', '4683', '4319', '1109']\n"]}], "source": ["# to_process = []\n", "# for sample_id, sample in dataset.samples.items():\n", "#     has_cellbodies = (sample.cellbodies_mask is not None) or (sample.cellbodies_multimask is not None)\n", "#     has_dapi_multi = sample.dapi_multi_mask is not None\n", "#     if has_cellbodies and not has_dapi_multi:\n", "#         to_process.append(sample_id)\n", "\n", "# print(f\"—— 一共发现 {len(to_process)} 个样本，需要先跑 apply_nuc_pipeline，然后生成 ROI：{to_process}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "a9a70af0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "[INFO] 开始处理样本：8550\n", "  • 运行 apply_nuc_pipeline...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Applied cellposeSAM\n", "  • 生成 ROI ZIP: roi_zips/8550_nuclei_roi.zip ...\n", "ROI for label 1 saved as 'roi_1.roi'.\n", "ROI for label 2 saved as 'roi_2.roi'.\n", "ROI for label 3 saved as 'roi_3.roi'.\n", "ROI for label 4 saved as 'roi_4.roi'.\n", "ROI for label 5 saved as 'roi_5.roi'.\n", "ROI for label 6 saved as 'roi_6.roi'.\n", "ROI for label 7 saved as 'roi_7.roi'.\n", "ROI for label 8 saved as 'roi_8.roi'.\n", "ROI for label 9 saved as 'roi_9.roi'.\n", "ROI for label 10 saved as 'roi_10.roi'.\n", "ROI for label 11 saved as 'roi_11.roi'.\n", "ROI for label 12 saved as 'roi_12.roi'.\n", "ROI for label 13 saved as 'roi_13.roi'.\n", "ROI for label 14 saved as 'roi_14.roi'.\n", "ROI for label 15 saved as 'roi_15.roi'.\n", "ROI for label 16 saved as 'roi_16.roi'.\n", "ROI for label 17 saved as 'roi_17.roi'.\n", "ROI for label 18 saved as 'roi_18.roi'.\n", "ROI for label 19 saved as 'roi_19.roi'.\n", "ROI for label 20 saved as 'roi_20.roi'.\n", "ROI for label 21 saved as 'roi_21.roi'.\n", "ROI for label 22 saved as 'roi_22.roi'.\n", "ROI for label 23 saved as 'roi_23.roi'.\n", "ROI for label 24 saved as 'roi_24.roi'.\n", "ROI for label 25 saved as 'roi_25.roi'.\n", "ROI for label 26 saved as 'roi_26.roi'.\n", "ROI for label 27 saved as 'roi_27.roi'.\n", "ROI for label 28 saved as 'roi_28.roi'.\n", "ROI for label 29 saved as 'roi_29.roi'.\n", "ROI for label 30 saved as 'roi_30.roi'.\n", "ROI for label 31 saved as 'roi_31.roi'.\n", "ROI for label 32 saved as 'roi_32.roi'.\n", "ROI for label 33 saved as 'roi_33.roi'.\n", "ROI for label 34 saved as 'roi_34.roi'.\n", "ROI for label 35 saved as 'roi_35.roi'.\n", "ROI for label 36 saved as 'roi_36.roi'.\n", "ROI for label 37 saved as 'roi_37.roi'.\n", "ROI for label 38 saved as 'roi_38.roi'.\n", "ROI for label 39 saved as 'roi_39.roi'.\n", "ROI for label 40 saved as 'roi_40.roi'.\n", "ROI for label 41 saved as 'roi_41.roi'.\n", "ROI for label 42 saved as 'roi_42.roi'.\n", "ROI for label 43 saved as 'roi_43.roi'.\n", "ROI for label 44 saved as 'roi_44.roi'.\n", "ROI for label 45 saved as 'roi_45.roi'.\n", "ROI for label 46 saved as 'roi_46.roi'.\n", "ROI for label 47 saved as 'roi_47.roi'.\n", "ROI for label 48 saved as 'roi_48.roi'.\n", "ROI for label 49 saved as 'roi_49.roi'.\n", "ROI for label 50 saved as 'roi_50.roi'.\n", "ROI for label 51 saved as 'roi_51.roi'.\n", "ROI for label 52 saved as 'roi_52.roi'.\n", "ROI for label 53 saved as 'roi_53.roi'.\n", "ROI for label 54 saved as 'roi_54.roi'.\n", "ROI for label 55 saved as 'roi_55.roi'.\n", "ROI for label 56 saved as 'roi_56.roi'.\n", "ROI for label 57 saved as 'roi_57.roi'.\n", "ROI for label 58 saved as 'roi_58.roi'.\n", "ROI for label 59 saved as 'roi_59.roi'.\n", "ROI for label 60 saved as 'roi_60.roi'.\n", "ROI for label 61 saved as 'roi_61.roi'.\n", "ROI for label 62 saved as 'roi_62.roi'.\n", "ROI for label 63 saved as 'roi_63.roi'.\n", "ROI for label 64 saved as 'roi_64.roi'.\n", "ROI for label 65 saved as 'roi_65.roi'.\n", "ROI for label 66 saved as 'roi_66.roi'.\n", "ROI for label 67 saved as 'roi_67.roi'.\n", "ROI for label 68 saved as 'roi_68.roi'.\n", "ROI for label 69 saved as 'roi_69.roi'.\n", "ROI for label 70 saved as 'roi_70.roi'.\n", "ROI for label 71 saved as 'roi_71.roi'.\n", "ROI for label 72 saved as 'roi_72.roi'.\n", "ROI for label 73 saved as 'roi_73.roi'.\n", "ROI for label 74 saved as 'roi_74.roi'.\n", "ROI for label 75 saved as 'roi_75.roi'.\n", "ROI for label 76 saved as 'roi_76.roi'.\n", "ROI for label 77 saved as 'roi_77.roi'.\n", "ROI for label 78 saved as 'roi_78.roi'.\n", "ROI for label 79 saved as 'roi_79.roi'.\n", "ROI for label 80 saved as 'roi_80.roi'.\n", "ROI for label 81 saved as 'roi_81.roi'.\n", "ROI for label 82 saved as 'roi_82.roi'.\n", "ROI for label 83 saved as 'roi_83.roi'.\n", "ROI for label 84 saved as 'roi_84.roi'.\n", "ROI for label 85 saved as 'roi_85.roi'.\n", "ROI for label 86 saved as 'roi_86.roi'.\n", "ROI for label 87 saved as 'roi_87.roi'.\n", "ROI for label 88 saved as 'roi_88.roi'.\n", "ROI for label 89 saved as 'roi_89.roi'.\n", "ROI for label 90 saved as 'roi_90.roi'.\n", "ROI for label 91 saved as 'roi_91.roi'.\n", "ROI for label 92 saved as 'roi_92.roi'.\n", "ROI for label 93 saved as 'roi_93.roi'.\n", "ROI for label 94 saved as 'roi_94.roi'.\n", "ROI for label 95 saved as 'roi_95.roi'.\n", "ROI for label 96 saved as 'roi_96.roi'.\n", "ROI for label 97 saved as 'roi_97.roi'.\n", "ROI for label 98 saved as 'roi_98.roi'.\n", "ROI for label 99 saved as 'roi_99.roi'.\n", "ROI for label 100 saved as 'roi_100.roi'.\n", "ROI for label 101 saved as 'roi_101.roi'.\n", "ROI for label 102 saved as 'roi_102.roi'.\n", "ROI for label 103 saved as 'roi_103.roi'.\n", "ROI for label 104 saved as 'roi_104.roi'.\n", "ROI for label 105 saved as 'roi_105.roi'.\n", "ROI for label 106 saved as 'roi_106.roi'.\n", "ROI for label 107 saved as 'roi_107.roi'.\n", "ROI for label 108 saved as 'roi_108.roi'.\n", "ROI for label 109 saved as 'roi_109.roi'.\n", "ROI for label 110 saved as 'roi_110.roi'.\n", "ROI for label 111 saved as 'roi_111.roi'.\n", "ROI for label 112 saved as 'roi_112.roi'.\n", "ROI for label 113 saved as 'roi_113.roi'.\n", "ROI for label 114 saved as 'roi_114.roi'.\n", "ROI for label 115 saved as 'roi_115.roi'.\n", "ROI for label 116 saved as 'roi_116.roi'.\n", "ROI for label 117 saved as 'roi_117.roi'.\n", "ROI for label 118 saved as 'roi_118.roi'.\n", "ROI for label 119 saved as 'roi_119.roi'.\n", "ROI for label 120 saved as 'roi_120.roi'.\n", "ROI for label 121 saved as 'roi_121.roi'.\n", "ROI for label 122 saved as 'roi_122.roi'.\n", "ROI for label 123 saved as 'roi_123.roi'.\n", "ROI for label 124 saved as 'roi_124.roi'.\n", "ROI for label 125 saved as 'roi_125.roi'.\n", "ROI for label 126 saved as 'roi_126.roi'.\n", "ROI for label 127 saved as 'roi_127.roi'.\n", "ROI for label 128 saved as 'roi_128.roi'.\n", "ROI for label 129 saved as 'roi_129.roi'.\n", "ROI for label 130 saved as 'roi_130.roi'.\n", "ROI for label 131 saved as 'roi_131.roi'.\n", "ROI for label 132 saved as 'roi_132.roi'.\n", "ROI for label 133 saved as 'roi_133.roi'.\n", "ROI for label 134 saved as 'roi_134.roi'.\n", "ROI for label 135 saved as 'roi_135.roi'.\n", "ROI for label 136 saved as 'roi_136.roi'.\n", "ROI for label 137 saved as 'roi_137.roi'.\n", "ROI for label 138 saved as 'roi_138.roi'.\n", "ROI for label 139 saved as 'roi_139.roi'.\n", "ROI for label 140 saved as 'roi_140.roi'.\n", "ROI for label 141 saved as 'roi_141.roi'.\n", "ROI for label 142 saved as 'roi_142.roi'.\n", "ROI for label 143 saved as 'roi_143.roi'.\n", "ROI for label 144 saved as 'roi_144.roi'.\n", "ROI for label 145 saved as 'roi_145.roi'.\n", "ROI for label 146 saved as 'roi_146.roi'.\n", "ROI for label 147 saved as 'roi_147.roi'.\n", "ROI for label 148 saved as 'roi_148.roi'.\n", "ROI for label 149 saved as 'roi_149.roi'.\n", "ROI for label 150 saved as 'roi_150.roi'.\n", "ROI for label 151 saved as 'roi_151.roi'.\n", "ROI for label 152 saved as 'roi_152.roi'.\n", "ROI for label 153 saved as 'roi_153.roi'.\n", "ROI for label 154 saved as 'roi_154.roi'.\n", "ROI for label 155 saved as 'roi_155.roi'.\n", "ROI for label 156 saved as 'roi_156.roi'.\n", "ROI for label 157 saved as 'roi_157.roi'.\n", "ROI for label 158 saved as 'roi_158.roi'.\n", "ROI for label 159 saved as 'roi_159.roi'.\n", "ROI for label 160 saved as 'roi_160.roi'.\n", "ROI for label 161 saved as 'roi_161.roi'.\n", "ROI for label 162 saved as 'roi_162.roi'.\n", "ROI for label 163 saved as 'roi_163.roi'.\n", "ROI for label 164 saved as 'roi_164.roi'.\n", "ROI for label 165 saved as 'roi_165.roi'.\n", "ROI for label 166 saved as 'roi_166.roi'.\n", "ROI for label 167 saved as 'roi_167.roi'.\n", "ROI for label 168 saved as 'roi_168.roi'.\n", "ROI for label 169 saved as 'roi_169.roi'.\n", "ROI for label 170 saved as 'roi_170.roi'.\n", "ROI for label 171 saved as 'roi_171.roi'.\n", "ROI for label 172 saved as 'roi_172.roi'.\n", "ROI for label 173 saved as 'roi_173.roi'.\n", "ROI for label 174 saved as 'roi_174.roi'.\n", "ROI for label 175 saved as 'roi_175.roi'.\n", "ROI for label 176 saved as 'roi_176.roi'.\n", "ROI for label 177 saved as 'roi_177.roi'.\n", "ROI for label 178 saved as 'roi_178.roi'.\n", "ROI for label 179 saved as 'roi_179.roi'.\n", "ROI for label 180 saved as 'roi_180.roi'.\n", "ROI for label 181 saved as 'roi_181.roi'.\n", "ROI for label 182 saved as 'roi_182.roi'.\n", "ROI for label 183 saved as 'roi_183.roi'.\n", "ROI for label 184 saved as 'roi_184.roi'.\n", "ROI for label 185 saved as 'roi_185.roi'.\n", "ROI for label 186 saved as 'roi_186.roi'.\n", "ROI for label 187 saved as 'roi_187.roi'.\n", "ROI for label 188 saved as 'roi_188.roi'.\n", "ROI for label 189 saved as 'roi_189.roi'.\n", "ROI for label 190 saved as 'roi_190.roi'.\n", "ROI for label 191 saved as 'roi_191.roi'.\n", "ROI for label 192 saved as 'roi_192.roi'.\n", "ROI for label 193 saved as 'roi_193.roi'.\n", "ROI for label 194 saved as 'roi_194.roi'.\n", "ROI for label 195 saved as 'roi_195.roi'.\n", "ROI for label 196 saved as 'roi_196.roi'.\n", "ROI for label 197 saved as 'roi_197.roi'.\n", "ROI for label 198 saved as 'roi_198.roi'.\n", "ROI for label 199 saved as 'roi_199.roi'.\n", "ROI for label 200 saved as 'roi_200.roi'.\n", "ROI for label 201 saved as 'roi_201.roi'.\n", "ROI for label 202 saved as 'roi_202.roi'.\n", "ROI for label 203 saved as 'roi_203.roi'.\n", "ROI for label 204 saved as 'roi_204.roi'.\n", "ROI for label 205 saved as 'roi_205.roi'.\n", "ROI for label 206 saved as 'roi_206.roi'.\n", "ROI for label 207 saved as 'roi_207.roi'.\n", "ROI for label 208 saved as 'roi_208.roi'.\n", "ROI for label 209 saved as 'roi_209.roi'.\n", "ROI for label 210 saved as 'roi_210.roi'.\n", "ROI for label 211 saved as 'roi_211.roi'.\n", "ROI for label 212 saved as 'roi_212.roi'.\n", "ROI for label 213 saved as 'roi_213.roi'.\n", "ROI for label 214 saved as 'roi_214.roi'.\n", "ROI for label 215 saved as 'roi_215.roi'.\n", "ROI for label 216 saved as 'roi_216.roi'.\n", "ROI for label 217 saved as 'roi_217.roi'.\n", "ROI for label 218 saved as 'roi_218.roi'.\n", "ROI for label 219 saved as 'roi_219.roi'.\n", "ROI for label 220 saved as 'roi_220.roi'.\n", "ROI for label 221 saved as 'roi_221.roi'.\n", "ROI for label 222 saved as 'roi_222.roi'.\n", "ROI for label 223 saved as 'roi_223.roi'.\n", "ROI for label 224 saved as 'roi_224.roi'.\n", "ROI for label 225 saved as 'roi_225.roi'.\n", "ROI for label 226 saved as 'roi_226.roi'.\n", "ROI for label 227 saved as 'roi_227.roi'.\n", "ROI for label 228 saved as 'roi_228.roi'.\n", "ROI for label 229 saved as 'roi_229.roi'.\n", "ROI for label 230 saved as 'roi_230.roi'.\n", "ROI for label 231 saved as 'roi_231.roi'.\n", "ROI for label 232 saved as 'roi_232.roi'.\n", "ROI for label 233 saved as 'roi_233.roi'.\n", "ROI for label 234 saved as 'roi_234.roi'.\n", "ROI for label 235 saved as 'roi_235.roi'.\n", "ROI for label 236 saved as 'roi_236.roi'.\n", "ROI for label 237 saved as 'roi_237.roi'.\n", "ROI for label 238 saved as 'roi_238.roi'.\n", "ROI for label 239 saved as 'roi_239.roi'.\n", "ROI for label 240 saved as 'roi_240.roi'.\n", "ROI for label 241 saved as 'roi_241.roi'.\n", "ROI for label 242 saved as 'roi_242.roi'.\n", "ROI for label 243 saved as 'roi_243.roi'.\n", "ROI for label 244 saved as 'roi_244.roi'.\n", "ROI for label 245 saved as 'roi_245.roi'.\n", "ROI for label 246 saved as 'roi_246.roi'.\n", "ROI for label 247 saved as 'roi_247.roi'.\n", "ROI for label 248 saved as 'roi_248.roi'.\n", "ROI for label 249 saved as 'roi_249.roi'.\n", "ROI for label 250 saved as 'roi_250.roi'.\n", "ROI for label 251 saved as 'roi_251.roi'.\n", "ROI for label 252 saved as 'roi_252.roi'.\n", "ROI for label 253 saved as 'roi_253.roi'.\n", "ROI for label 254 saved as 'roi_254.roi'.\n", "ROI for label 255 saved as 'roi_255.roi'.\n", "ROI for label 256 saved as 'roi_256.roi'.\n", "ROI for label 257 saved as 'roi_257.roi'.\n", "ROI for label 258 saved as 'roi_258.roi'.\n", "ROI for label 259 saved as 'roi_259.roi'.\n", "ROI for label 260 saved as 'roi_260.roi'.\n", "ROI for label 261 saved as 'roi_261.roi'.\n", "ROI for label 262 saved as 'roi_262.roi'.\n", "ROI for label 263 saved as 'roi_263.roi'.\n", "ROI for label 264 saved as 'roi_264.roi'.\n", "ROI for label 265 saved as 'roi_265.roi'.\n", "ROI for label 266 saved as 'roi_266.roi'.\n", "ROI for label 267 saved as 'roi_267.roi'.\n", "ROI for label 268 saved as 'roi_268.roi'.\n", "ROI for label 269 saved as 'roi_269.roi'.\n", "ROI for label 270 saved as 'roi_270.roi'.\n", "ROI for label 271 saved as 'roi_271.roi'.\n", "ROI for label 272 saved as 'roi_272.roi'.\n", "ROI for label 273 saved as 'roi_273.roi'.\n", "ROI for label 274 saved as 'roi_274.roi'.\n", "ROI for label 275 saved as 'roi_275.roi'.\n", "ROI for label 276 saved as 'roi_276.roi'.\n", "ROI for label 277 saved as 'roi_277.roi'.\n", "ROI for label 278 saved as 'roi_278.roi'.\n", "ROI for label 279 saved as 'roi_279.roi'.\n", "ROI for label 280 saved as 'roi_280.roi'.\n", "ROI for label 281 saved as 'roi_281.roi'.\n", "ROI for label 282 saved as 'roi_282.roi'.\n", "ROI for label 283 saved as 'roi_283.roi'.\n", "ROI for label 284 saved as 'roi_284.roi'.\n", "ROI for label 285 saved as 'roi_285.roi'.\n", "ROI for label 286 saved as 'roi_286.roi'.\n", "ROI for label 287 saved as 'roi_287.roi'.\n", "ROI for label 288 saved as 'roi_288.roi'.\n", "ROI for label 289 saved as 'roi_289.roi'.\n", "ROI for label 290 saved as 'roi_290.roi'.\n", "ROI for label 291 saved as 'roi_291.roi'.\n", "ROI for label 292 saved as 'roi_292.roi'.\n", "ROI for label 293 saved as 'roi_293.roi'.\n", "ROI for label 294 saved as 'roi_294.roi'.\n", "ROI for label 295 saved as 'roi_295.roi'.\n", "ROI for label 296 saved as 'roi_296.roi'.\n", "ROI for label 297 saved as 'roi_297.roi'.\n", "ROI for label 298 saved as 'roi_298.roi'.\n", "ROI for label 299 saved as 'roi_299.roi'.\n", "ROI for label 300 saved as 'roi_300.roi'.\n", "ROI for label 301 saved as 'roi_301.roi'.\n", "ROI for label 302 saved as 'roi_302.roi'.\n", "ROI for label 303 saved as 'roi_303.roi'.\n", "ROI for label 304 saved as 'roi_304.roi'.\n", "ROI for label 305 saved as 'roi_305.roi'.\n", "ROI for label 306 saved as 'roi_306.roi'.\n", "ROI for label 307 saved as 'roi_307.roi'.\n", "ROI for label 308 saved as 'roi_308.roi'.\n", "ROI for label 309 saved as 'roi_309.roi'.\n", "ROI for label 310 saved as 'roi_310.roi'.\n", "ROI for label 311 saved as 'roi_311.roi'.\n", "ROI for label 312 saved as 'roi_312.roi'.\n", "ROI for label 313 saved as 'roi_313.roi'.\n", "ROI for label 314 saved as 'roi_314.roi'.\n", "ROI for label 315 saved as 'roi_315.roi'.\n", "ROI for label 316 saved as 'roi_316.roi'.\n", "ROI for label 317 saved as 'roi_317.roi'.\n", "ROI for label 318 saved as 'roi_318.roi'.\n", "ROI for label 319 saved as 'roi_319.roi'.\n", "ROI for label 320 saved as 'roi_320.roi'.\n", "ROI for label 321 saved as 'roi_321.roi'.\n", "ROI for label 322 saved as 'roi_322.roi'.\n", "ROI for label 323 saved as 'roi_323.roi'.\n", "ROI for label 324 saved as 'roi_324.roi'.\n", "ROI for label 325 saved as 'roi_325.roi'.\n", "ROI for label 326 saved as 'roi_326.roi'.\n", "ROI for label 327 saved as 'roi_327.roi'.\n", "ROI for label 328 saved as 'roi_328.roi'.\n", "ROI for label 329 saved as 'roi_329.roi'.\n", "ROI for label 330 saved as 'roi_330.roi'.\n", "ROI for label 331 saved as 'roi_331.roi'.\n", "ROI for label 332 saved as 'roi_332.roi'.\n", "ROI for label 333 saved as 'roi_333.roi'.\n", "ROI for label 334 saved as 'roi_334.roi'.\n", "ROI for label 335 saved as 'roi_335.roi'.\n", "ROI for label 336 saved as 'roi_336.roi'.\n", "ROI for label 337 saved as 'roi_337.roi'.\n", "ROI for label 338 saved as 'roi_338.roi'.\n", "ROI for label 339 saved as 'roi_339.roi'.\n", "ROI for label 340 saved as 'roi_340.roi'.\n", "ROI for label 341 saved as 'roi_341.roi'.\n", "ROI for label 342 saved as 'roi_342.roi'.\n", "ROI for label 343 saved as 'roi_343.roi'.\n", "ROI for label 344 saved as 'roi_344.roi'.\n", "ROI for label 345 saved as 'roi_345.roi'.\n", "ROI for label 346 saved as 'roi_346.roi'.\n", "ROI for label 347 saved as 'roi_347.roi'.\n", "ROI for label 348 saved as 'roi_348.roi'.\n", "ROI for label 349 saved as 'roi_349.roi'.\n", "ROI for label 350 saved as 'roi_350.roi'.\n", "ROI for label 351 saved as 'roi_351.roi'.\n", "ROI for label 352 saved as 'roi_352.roi'.\n", "ROI for label 353 saved as 'roi_353.roi'.\n", "ROI for label 354 saved as 'roi_354.roi'.\n", "ROI for label 355 saved as 'roi_355.roi'.\n", "ROI for label 356 saved as 'roi_356.roi'.\n", "ROI for label 357 saved as 'roi_357.roi'.\n", "ROI for label 358 saved as 'roi_358.roi'.\n", "ROI for label 359 saved as 'roi_359.roi'.\n", "ROI for label 360 saved as 'roi_360.roi'.\n", "ROI for label 361 saved as 'roi_361.roi'.\n", "ROI for label 362 saved as 'roi_362.roi'.\n", "ROI for label 363 saved as 'roi_363.roi'.\n", "ROI for label 364 saved as 'roi_364.roi'.\n", "ROI for label 365 saved as 'roi_365.roi'.\n", "ROI for label 366 saved as 'roi_366.roi'.\n", "ROI for label 367 saved as 'roi_367.roi'.\n", "ROI for label 368 saved as 'roi_368.roi'.\n", "ROI for label 369 saved as 'roi_369.roi'.\n", "ROI for label 370 saved as 'roi_370.roi'.\n", "ROI for label 371 saved as 'roi_371.roi'.\n", "ROI for label 372 saved as 'roi_372.roi'.\n", "ROI for label 373 saved as 'roi_373.roi'.\n", "ROI for label 374 saved as 'roi_374.roi'.\n", "ROI for label 375 saved as 'roi_375.roi'.\n", "ROI for label 376 saved as 'roi_376.roi'.\n", "ROI for label 377 saved as 'roi_377.roi'.\n", "ROI for label 378 saved as 'roi_378.roi'.\n", "ROI for label 379 saved as 'roi_379.roi'.\n", "ROI for label 380 saved as 'roi_380.roi'.\n", "ROI for label 381 saved as 'roi_381.roi'.\n", "ROI for label 382 saved as 'roi_382.roi'.\n", "ROI for label 383 saved as 'roi_383.roi'.\n", "ROI for label 384 saved as 'roi_384.roi'.\n", "ROI for label 385 saved as 'roi_385.roi'.\n", "ROI for label 386 saved as 'roi_386.roi'.\n", "ROI for label 387 saved as 'roi_387.roi'.\n", "ROI for label 388 saved as 'roi_388.roi'.\n", "ROI for label 389 saved as 'roi_389.roi'.\n", "ROI for label 390 saved as 'roi_390.roi'.\n", "ROI for label 391 saved as 'roi_391.roi'.\n", "ROI for label 392 saved as 'roi_392.roi'.\n", "ROI for label 393 saved as 'roi_393.roi'.\n", "ROI for label 394 saved as 'roi_394.roi'.\n", "ROI for label 395 saved as 'roi_395.roi'.\n", "ROI for label 396 saved as 'roi_396.roi'.\n", "ROI for label 397 saved as 'roi_397.roi'.\n", "ROI for label 398 saved as 'roi_398.roi'.\n", "  [OK] 样本 8550 的 nuclei ROI ZIP 已保存。\n", "\n", "[INFO] 开始处理样本：8917\n", "  • 运行 apply_nuc_pipeline...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[26], line 11\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# 1. 调用 apply_nuc_pipeline 生成 dapi_multi_mask\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m  • 运行 apply_nuc_pipeline...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 11\u001b[0m \u001b[43msample\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply_nuc_pipeline\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m sample\u001b[38;5;241m.\u001b[39mmasks \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m     14\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m  [WARN] 样本 \u001b[39m\u001b[38;5;132;01m{\u001b[39;00msid\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 在运行 apply_nuc_pipeline 后仍然没有 dapi_multi_mask，跳过。\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/ifimage/ifimage_tools.py:94\u001b[0m, in \u001b[0;36mImageSample.apply_nuc_pipeline\u001b[0;34m(self, methods)\u001b[0m\n\u001b[1;32m     92\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m     93\u001b[0m     model \u001b[38;5;241m=\u001b[39m available_models[method]()\n\u001b[0;32m---> 94\u001b[0m     masks, _, _ \u001b[38;5;241m=\u001b[39m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43meval\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdapi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdiameter\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchannels\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     95\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmasks[method] \u001b[38;5;241m=\u001b[39m masks\n\u001b[1;32m     96\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mApplied \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmethod\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/cellpose/models.py:314\u001b[0m, in \u001b[0;36mCellposeModel.eval\u001b[0;34m(self, x, batch_size, resample, channels, channel_axis, z_axis, normalize, invert, rescale, diameter, flow_threshold, cellprob_threshold, do_3D, anisotropy, flow3D_smooth, stitch_threshold, min_size, max_size_fraction, niter, augment, tile_overlap, bsize, compute_masks, progress)\u001b[0m\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(anisotropy, (\u001b[38;5;28mfloat\u001b[39m, \u001b[38;5;28mint\u001b[39m)) \u001b[38;5;129;01mand\u001b[39;00m image_scaling:\n\u001b[1;32m    312\u001b[0m     anisotropy \u001b[38;5;241m=\u001b[39m image_scaling \u001b[38;5;241m*\u001b[39m anisotropy\n\u001b[0;32m--> 314\u001b[0m dP, cellprob, styles \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_net\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    315\u001b[0m \u001b[43m    \u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m    316\u001b[0m \u001b[43m    \u001b[49m\u001b[43maugment\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maugment\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m    317\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbatch_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m    318\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtile_overlap\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtile_overlap\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m    319\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbsize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbsize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    320\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdo_3D\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdo_3D\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m    321\u001b[0m \u001b[43m    \u001b[49m\u001b[43manisotropy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43manisotropy\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    323\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m do_3D:    \n\u001b[1;32m    324\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m flow3D_smooth \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/cellpose/models.py:400\u001b[0m, in \u001b[0;36mCellposeModel._run_net\u001b[0;34m(self, x, augment, batch_size, tile_overlap, bsize, anisotropy, do_3D)\u001b[0m\n\u001b[1;32m    398\u001b[0m     dP \u001b[38;5;241m=\u001b[39m yf[\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m, :\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;241m.\u001b[39mtranspose((\u001b[38;5;241m3\u001b[39m, \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m2\u001b[39m))\n\u001b[1;32m    399\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 400\u001b[0m     yf, styles \u001b[38;5;241m=\u001b[39m \u001b[43mrun_net\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnet\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbsize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbsize\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maugment\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maugment\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    401\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbatch_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\n\u001b[1;32m    402\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mtile_overlap\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtile_overlap\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m    403\u001b[0m \u001b[43m                        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    404\u001b[0m     cellprob \u001b[38;5;241m=\u001b[39m yf[\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m    405\u001b[0m     dP \u001b[38;5;241m=\u001b[39m yf[\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m3\u001b[39m:\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;241m.\u001b[39mtranspose((\u001b[38;5;241m3\u001b[39m, \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m2\u001b[39m))\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/cellpose/core.py:229\u001b[0m, in \u001b[0;36mrun_net\u001b[0;34m(net, imgi, batch_size, augment, tile_overlap, bsize, rsz)\u001b[0m\n\u001b[1;32m    227\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m j \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m0\u001b[39m, IMGa\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m], batch_size):\n\u001b[1;32m    228\u001b[0m     bslc \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mslice\u001b[39m(j, \u001b[38;5;28mmin\u001b[39m(j \u001b[38;5;241m+\u001b[39m batch_size, IMGa\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m]))\n\u001b[0;32m--> 229\u001b[0m     ya0, stylea0 \u001b[38;5;241m=\u001b[39m \u001b[43m_forward\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnet\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mIMGa\u001b[49m\u001b[43m[\u001b[49m\u001b[43mbslc\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    230\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m j \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    231\u001b[0m         nout \u001b[38;5;241m=\u001b[39m ya0\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m1\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/cellpose/core.py:157\u001b[0m, in \u001b[0;36m_forward\u001b[0;34m(net, x)\u001b[0m\n\u001b[1;32m    155\u001b[0m net\u001b[38;5;241m.\u001b[39meval()\n\u001b[1;32m    156\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m torch\u001b[38;5;241m.\u001b[39mno_grad():\n\u001b[0;32m--> 157\u001b[0m     y, style \u001b[38;5;241m=\u001b[39m \u001b[43mnet\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m)\u001b[49m[:\u001b[38;5;241m2\u001b[39m]\n\u001b[1;32m    158\u001b[0m \u001b[38;5;28;01mdel\u001b[39;00m X\n\u001b[1;32m    159\u001b[0m y \u001b[38;5;241m=\u001b[39m _from_device(y)\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1751\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1749\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1751\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1762\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1757\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1758\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1760\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1761\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1762\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1764\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1765\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/cellpose/vit_sam.py:68\u001b[0m, in \u001b[0;36mTransformer.forward\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     66\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m     67\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m blk \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mencoder\u001b[38;5;241m.\u001b[39mblocks:\n\u001b[0;32m---> 68\u001b[0m         x \u001b[38;5;241m=\u001b[39m \u001b[43mblk\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     70\u001b[0m x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mencoder\u001b[38;5;241m.\u001b[39mneck(x\u001b[38;5;241m.\u001b[39mpermute(\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m3\u001b[39m, \u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m2\u001b[39m))\n\u001b[1;32m     72\u001b[0m \u001b[38;5;66;03m# readout is changed here\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1751\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1749\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1751\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1762\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1757\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1758\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1760\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1761\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1762\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1764\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1765\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/segment_anything/modeling/image_encoder.py:180\u001b[0m, in \u001b[0;36mBlock.forward\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m    177\u001b[0m     x \u001b[38;5;241m=\u001b[39m window_unpartition(x, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mwindow_size, pad_hw, (H, W))\n\u001b[1;32m    179\u001b[0m x \u001b[38;5;241m=\u001b[39m shortcut \u001b[38;5;241m+\u001b[39m x\n\u001b[0;32m--> 180\u001b[0m x \u001b[38;5;241m=\u001b[39m x \u001b[38;5;241m+\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmlp\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnorm2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m x\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1751\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1749\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1751\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1762\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1757\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1758\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1760\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1761\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1762\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1764\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1765\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/segment_anything/modeling/common.py:26\u001b[0m, in \u001b[0;36mMLPBlock.forward\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     25\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, x: torch\u001b[38;5;241m.\u001b[39mTensor) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m torch\u001b[38;5;241m.\u001b[39mTensor:\n\u001b[0;32m---> 26\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlin2(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mact\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlin1\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1751\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1749\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1751\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/module.py:1762\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1757\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1758\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1760\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1761\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1762\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1764\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1765\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/torch/nn/modules/activation.py:734\u001b[0m, in \u001b[0;36mGELU.forward\u001b[0;34m(self, input)\u001b[0m\n\u001b[1;32m    733\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m: Tensor) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Tensor:\n\u001b[0;32m--> 734\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mF\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgelu\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mapproximate\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapproximate\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# 输出目录\n", "output_zip_dir = \"roi_zips\"\n", "os.makedirs(output_zip_dir, exist_ok=True)\n", "\n", "for sid in to_process:\n", "    sample = dataset.samples[sid]\n", "    print(f\"\\n[INFO] 开始处理样本：{sid}\")\n", "\n", "    # 1. 调用 apply_nuc_pipeline 生成 dapi_multi_mask\n", "    print(f\"  • 运行 apply_nuc_pipeline...\")\n", "    sample.apply_nuc_pipeline()\n", "\n", "    if sample.masks is None:\n", "        print(f\"  [WARN] 样本 {sid} 在运行 apply_nuc_pipeline 后仍然没有 dapi_multi_mask，跳过。\")\n", "        continue\n", "\n", "    dapi_mask = sample.masks.copy()[\"cellposeSAM\"]\n", "    # 如果是 0/1 二值图，就先做 connected-components 标号\n", "    from skimage.measure import label as sklabel\n", "    if dapi_mask.max() <= 1:\n", "        dapi_mask = sklabel(dapi_mask)\n", "\n", "    # 3. 调用 mask_to_rois_zip，把 dapi_mask 转成 ImageJ ROI 并打包\n", "    celltype=sample.celltype\n", "    zip_path = os.path.join(output_zip_dir, f\"{celltype}_{sid}_nuclei_roi.zip\")\n", "    print(f\"  • 生成 ROI ZIP: {zip_path} ...\")\n", "    mask_to_rois_zip(dapi_mask, zip_path)\n", "\n", "    print(f\"  [OK] 样本 {sid} 的 nuclei ROI ZIP 已保存。\")\n", "\n", "print(\"\\n全部样本处理完毕，请到 “roi_zips” 目录查看各个 *_nuclei_roi.zip 文件。\")"]}, {"cell_type": "code", "execution_count": null, "id": "eaf04ce9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cellpose", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 5}