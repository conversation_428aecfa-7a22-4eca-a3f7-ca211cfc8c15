{"cells": [{"cell_type": "code", "execution_count": 1, "id": "08092063", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Welcome to CellposeSAM, cellpose v\n", "cellpose version: \t4.0.4 \n", "platform:       \tlinux \n", "python version: \t3.10.16 \n", "torch version:  \t2.7.0+cu126! The neural network component of\n", "CPSAM is much larger than in previous versions and CPU excution is slow. \n", "We encourage users to use GPU/MPS if available. \n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-07 04:54:47.681079: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2025-06-07 04:54:47.750752: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2025-06-07 04:54:50.147251: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n"]}], "source": ["import importlib\n", "import ifimage_tools\n", "importlib.reload(ifimage_tools)\n", "import pandas as pd\n", "import warnings\n", "import pickle"]}, {"cell_type": "code", "execution_count": 3, "id": "5d9a6566", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "image_dir = \"Reorgnized Ground Truth\"\n", "masks_dir = \"Reorgnized Ground Truth/mask\"\n", "dataset = ifimage_tools.IfImageDataset(image_dir, masks_dir, {})\n", "dataset.load_data()\n", "\n", "old_version_sample_ids = [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]\n", "for sample_id in old_version_sample_ids:\n", "    if sample_id in dataset.samples:\n", "        del dataset.samples[sample_id]"]}, {"cell_type": "code", "execution_count": 4, "id": "a3bb0952", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5803: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5803/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12781: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12781/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12779: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12779/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12786: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12786/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 10594: Applied cellposeSAM\n", "Saved cellposeSAM → mask/10594/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8408jan22: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8408jan22/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 15591: Applied cellposeSAM\n", "Saved cellposeSAM → mask/15591/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12793: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12793/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 15972: Applied cellposeSAM\n", "Saved cellposeSAM → mask/15972/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12794: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12794/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1120: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1120/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12795: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12795/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12792: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12792/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12787: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12787/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12780: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12780/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 2529: Applied cellposeSAM\n", "Saved cellposeSAM → mask/2529/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8265: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8265/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 15973: Applied cellposeSAM\n", "Saved cellposeSAM → mask/15973/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12798: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12798/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8407jan22: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8407jan22/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8406jan22: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8406jan22/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12799: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12799/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8405jan22: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8405jan22/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 15970: Applied cellposeSAM\n", "Saved cellposeSAM → mask/15970/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12791: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12791/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12784: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12784/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 15968: Applied cellposeSAM\n", "Saved cellposeSAM → mask/15968/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12789: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12789/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12783: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12783/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12796: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12796/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 15969: Applied cellposeSAM\n", "Saved cellposeSAM → mask/15969/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12797: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12797/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12782: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12782/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 15971: Applied cellposeSAM\n", "Saved cellposeSAM → mask/15971/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12785: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12785/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12790: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12790/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 12788: Applied cellposeSAM\n", "Saved cellposeSAM → mask/12788/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8409: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8409/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6390v2: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6390v2/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8550: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8550/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8917: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8917/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5191: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5191/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6833: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6833/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 10166: Applied cellposeSAM\n", "Saved cellposeSAM → mask/10166/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 9106: Applied cellposeSAM\n", "Saved cellposeSAM → mask/9106/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4642: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4642/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5792: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5792/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 10061: Applied cellposeSAM\n", "Saved cellposeSAM → mask/10061/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1110: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1110/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7925: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7925/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5863: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5863/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1112: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1112/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7739: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7739/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5059: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5059/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7113: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7113/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 3569: Applied cellposeSAM\n", "Saved cellposeSAM → mask/3569/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7685: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7685/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8224: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8224/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4515: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4515/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7870: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7870/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6790: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6790/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7962: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7962/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1111: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1111/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8746: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8746/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 3532: Applied cellposeSAM\n", "Saved cellposeSAM → mask/3532/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 3999: Applied cellposeSAM\n", "Saved cellposeSAM → mask/3999/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 3527: Applied cellposeSAM\n", "Saved cellposeSAM → mask/3527/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7071: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7071/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5923: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5923/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5789: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5789/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5794: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5794/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 9755: Applied cellposeSAM\n", "Saved cellposeSAM → mask/9755/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6020: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6020/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 9170: Applied cellposeSAM\n", "Saved cellposeSAM → mask/9170/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 10015: Applied cellposeSAM\n", "Saved cellposeSAM → mask/10015/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1116: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1116/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4238: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4238/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1114: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1114/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5795: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5795/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4548: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4548/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8517: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8517/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 9783: Applied cellposeSAM\n", "Saved cellposeSAM → mask/9783/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1108: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1108/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8310: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8310/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 9472: Applied cellposeSAM\n", "Saved cellposeSAM → mask/9472/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4736: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4736/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 7319: Applied cellposeSAM\n", "Saved cellposeSAM → mask/7319/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 9866: Applied cellposeSAM\n", "Saved cellposeSAM → mask/9866/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6212: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6212/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 8942: Applied cellposeSAM\n", "Saved cellposeSAM → mask/8942/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1113: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1113/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6748: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6748/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1115: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1115/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 5410: Applied cellposeSAM\n", "Saved cellposeSAM → mask/5410/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4201: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4201/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6523: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6523/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 6466: Applied cellposeSAM\n", "Saved cellposeSAM → mask/6466/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4683: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4683/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 4319: Applied cellposeSAM\n", "Saved cellposeSAM → mask/4319/cellposeSAM.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["channels deprecated in v4.0.1+. If data contain more than 3 channels, only the first 3 channels will be used\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[OK] Sample 1109: Applied cellposeSAM\n", "Saved cellposeSAM → mask/1109/cellposeSAM.npy\n"]}], "source": ["output_root = \"mask\"\n", "os.makedirs(output_root, exist_ok=True)\n", "\n", "for sample_id, sample in dataset.samples.items():\n", "    # 跑 nucleus segmentation pipeline\n", "    sample.apply_nuc_pipeline()\n", "    \n", "    # 给每个 sample 创建自己的子文件夹：mask/<sample_id>/\n", "    sample_dir = os.path.join(output_root, sample_id)\n", "    os.makedirs(sample_dir, exist_ok=True)\n", "    \n", "    # 遍历 sample.masks，把不为空的 mask 存成 .npy\n", "    for mask_name, mask_array in sample.masks.items():\n", "        if mask_array is None:\n", "            continue\n", "        out_path = os.path.join(sample_dir, f\"{mask_name}.npy\")\n", "        np.save(out_path, mask_array)\n", "        print(f\"Saved {mask_name} → {out_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "e9989c1c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ifimage", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}