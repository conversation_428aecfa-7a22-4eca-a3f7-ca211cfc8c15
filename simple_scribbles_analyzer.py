#!/usr/bin/env python3
"""
Simple Scribbles Analyzer

This script analyzes the scribbles directory structure without visualization
to help verify file organization and identify any issues.
"""

import os
import re
from pathlib import Path
import zipfile

class ScribblesAnalyzer:
    def __init__(self, scribbles_dir="scribbles"):
        """Initialize the analyzer"""
        self.scribbles_dir = Path(scribbles_dir)
        self.samples = self._scan_samples()
    
    def _scan_samples(self):
        """Scan scribbles directory and organize sample information"""
        samples = {}
        
        if not self.scribbles_dir.exists():
            print(f"Error: Scribbles directory '{self.scribbles_dir}' not found!")
            return samples
        
        # Find all celltype directories
        celltype_dirs = [d for d in self.scribbles_dir.iterdir() 
                        if d.is_dir() and d.name.endswith("_Scribbled")]
        
        print(f"Found {len(celltype_dirs)} cell type directories:")
        for celltype_dir in celltype_dirs:
            print(f"  - {celltype_dir.name}")
        
        for celltype_dir in celltype_dirs:
            # Extract celltype from directory name
            celltype = celltype_dir.name.replace("_Scribbled", "").lower()
            
            # Find all sample directories
            sample_dirs = [d for d in celltype_dir.iterdir() if d.is_dir()]
            print(f"\n{celltype_dir.name}: {len(sample_dirs)} samples")
            
            for sample_dir in sample_dirs:
                # Extract sample info
                match = re.match(r"^([A-Za-z0-9]+)_(\d+)$", sample_dir.name)
                if not match:
                    print(f"  WARNING: Cannot parse sample directory name: {sample_dir.name}")
                    continue
                    
                sample_celltype = match.group(1).lower()
                sample_id = match.group(2)
                sample_key = f"{sample_celltype}_{sample_id}"
                
                # Verify celltype consistency
                if sample_celltype != celltype:
                    print(f"  WARNING: Celltype mismatch in {sample_dir.name} (expected {celltype}, got {sample_celltype})")
                
                # Scan files in sample directory
                sample_info = {
                    'celltype': sample_celltype,
                    'sample_id': sample_id,
                    'path': sample_dir,
                    'dapi_file': None,
                    'marker_file': None,
                    'roi_files': [],
                    'other_files': [],
                    'all_files': []
                }
                
                for file_path in sample_dir.iterdir():
                    if file_path.is_file():
                        sample_info['all_files'].append(file_path)
                        filename = file_path.name.lower()
                        
                        if filename.endswith('.tiff') or filename.endswith('.tif'):
                            if 'b0c0' in filename:
                                sample_info['dapi_file'] = file_path
                            elif 'b0c1' in filename:
                                sample_info['marker_file'] = file_path
                            else:
                                sample_info['other_files'].append(file_path)
                        elif filename.endswith('.zip') and 'roi' in filename:
                            sample_info['roi_files'].append(file_path)
                        else:
                            sample_info['other_files'].append(file_path)
                
                samples[sample_key] = sample_info
        
        return samples
    
    def analyze_roi_files(self, sample_key):
        """Analyze ROI files for a specific sample"""
        if sample_key not in self.samples:
            print(f"Sample {sample_key} not found!")
            return
        
        sample_info = self.samples[sample_key]
        print(f"\nAnalyzing ROI files for {sample_key}:")
        
        for roi_file in sample_info['roi_files']:
            print(f"\n  ROI File: {roi_file.name}")
            try:
                with zipfile.ZipFile(roi_file, 'r') as zf:
                    roi_names = [name for name in zf.namelist() if name.endswith('.roi')]
                    print(f"    Contains {len(roi_names)} ROI files")
                    
                    if len(roi_names) <= 10:  # Show details for small numbers
                        for roi_name in roi_names:
                            print(f"      - {roi_name}")
                    else:
                        print(f"      - {roi_names[0]}")
                        print(f"      - ... ({len(roi_names)-2} more)")
                        print(f"      - {roi_names[-1]}")
                        
            except Exception as e:
                print(f"    ERROR: Cannot read ZIP file - {str(e)}")
    
    def create_summary_report(self):
        """Create a comprehensive summary report"""
        print("\n" + "="*80)
        print("SCRIBBLES DATASET ANALYSIS REPORT")
        print("="*80)
        
        # Overall statistics
        total_samples = len(self.samples)
        celltypes = set(info['celltype'] for info in self.samples.values())
        
        print(f"Total samples: {total_samples}")
        print(f"Cell types: {len(celltypes)} ({', '.join(sorted(celltypes))})")
        
        # Count by completeness
        complete_samples = 0
        missing_dapi = 0
        missing_marker = 0
        missing_roi = 0
        
        for sample_info in self.samples.values():
            has_dapi = sample_info['dapi_file'] is not None
            has_marker = sample_info['marker_file'] is not None
            has_roi = len(sample_info['roi_files']) > 0
            
            if has_dapi and has_marker and has_roi:
                complete_samples += 1
            
            if not has_dapi:
                missing_dapi += 1
            if not has_marker:
                missing_marker += 1
            if not has_roi:
                missing_roi += 1
        
        print(f"\nCompleteness:")
        print(f"  Complete samples (DAPI + Marker + ROI): {complete_samples}")
        print(f"  Missing DAPI: {missing_dapi}")
        print(f"  Missing Marker: {missing_marker}")
        print(f"  Missing ROI: {missing_roi}")
        
        # Count by cell type
        print(f"\nBreakdown by cell type:")
        celltype_stats = {}
        for sample_info in self.samples.values():
            celltype = sample_info['celltype']
            if celltype not in celltype_stats:
                celltype_stats[celltype] = {
                    'total': 0, 'complete': 0, 'missing_dapi': 0, 
                    'missing_marker': 0, 'missing_roi': 0
                }
            
            celltype_stats[celltype]['total'] += 1
            
            has_dapi = sample_info['dapi_file'] is not None
            has_marker = sample_info['marker_file'] is not None
            has_roi = len(sample_info['roi_files']) > 0
            
            if has_dapi and has_marker and has_roi:
                celltype_stats[celltype]['complete'] += 1
            if not has_dapi:
                celltype_stats[celltype]['missing_dapi'] += 1
            if not has_marker:
                celltype_stats[celltype]['missing_marker'] += 1
            if not has_roi:
                celltype_stats[celltype]['missing_roi'] += 1
        
        print(f"{'Type':<8} {'Total':<6} {'Complete':<9} {'No DAPI':<8} {'No Marker':<10} {'No ROI':<7}")
        print("-" * 60)
        for celltype in sorted(celltype_stats.keys()):
            stats = celltype_stats[celltype]
            print(f"{celltype:<8} {stats['total']:<6} {stats['complete']:<9} "
                  f"{stats['missing_dapi']:<8} {stats['missing_marker']:<10} {stats['missing_roi']:<7}")
    
    def list_problematic_samples(self):
        """List samples with missing components"""
        print("\n" + "="*80)
        print("PROBLEMATIC SAMPLES")
        print("="*80)
        
        problems_found = False
        
        for sample_key in sorted(self.samples.keys()):
            sample_info = self.samples[sample_key]
            issues = []
            
            if not sample_info['dapi_file']:
                issues.append('No DAPI (b0c0)')
            if not sample_info['marker_file']:
                issues.append('No Marker (b0c1)')
            if not sample_info['roi_files']:
                issues.append('No ROI files')
            
            if issues:
                problems_found = True
                print(f"\n{sample_key}:")
                print(f"  Path: {sample_info['path']}")
                print(f"  Issues: {', '.join(issues)}")
                print(f"  All files found:")
                for file_path in sample_info['all_files']:
                    print(f"    - {file_path.name}")
        
        if not problems_found:
            print("No problematic samples found! All samples have DAPI, Marker, and ROI files.")
    
    def show_file_patterns(self):
        """Show common file naming patterns"""
        print("\n" + "="*80)
        print("FILE NAMING PATTERNS")
        print("="*80)
        
        dapi_patterns = set()
        marker_patterns = set()
        roi_patterns = set()
        other_patterns = set()
        
        for sample_info in self.samples.values():
            if sample_info['dapi_file']:
                dapi_patterns.add(sample_info['dapi_file'].name)
            if sample_info['marker_file']:
                marker_patterns.add(sample_info['marker_file'].name)
            for roi_file in sample_info['roi_files']:
                roi_patterns.add(roi_file.name)
            for other_file in sample_info['other_files']:
                other_patterns.add(other_file.name)
        
        print(f"DAPI file patterns ({len(dapi_patterns)} unique):")
        for pattern in sorted(list(dapi_patterns)[:10]):  # Show first 10
            print(f"  {pattern}")
        if len(dapi_patterns) > 10:
            print(f"  ... and {len(dapi_patterns) - 10} more")
        
        print(f"\nMarker file patterns ({len(marker_patterns)} unique):")
        for pattern in sorted(list(marker_patterns)[:10]):
            print(f"  {pattern}")
        if len(marker_patterns) > 10:
            print(f"  ... and {len(marker_patterns) - 10} more")
        
        print(f"\nROI file patterns ({len(roi_patterns)} unique):")
        for pattern in sorted(list(roi_patterns)):
            print(f"  {pattern}")
        
        if other_patterns:
            print(f"\nOther file patterns ({len(other_patterns)} unique):")
            for pattern in sorted(list(other_patterns)[:10]):
                print(f"  {pattern}")
            if len(other_patterns) > 10:
                print(f"  ... and {len(other_patterns) - 10} more")
    
    def interactive_explorer(self):
        """Interactive command-line explorer"""
        print("\n" + "="*60)
        print("INTERACTIVE SCRIBBLES EXPLORER")
        print("="*60)
        print("Commands:")
        print("  'summary' - Show summary report")
        print("  'problems' - List problematic samples")
        print("  'patterns' - Show file naming patterns")
        print("  'analyze <sample>' - Analyze specific sample")
        print("  'list [celltype]' - List samples (optionally filtered)")
        print("  'roi <sample>' - Analyze ROI files for sample")
        print("  'quit' or 'q' - Exit")
        print("-"*60)
        
        while True:
            command = input("\nEnter command: ").strip().lower()
            
            if command in ['quit', 'q']:
                break
            elif command == 'summary':
                self.create_summary_report()
            elif command == 'problems':
                self.list_problematic_samples()
            elif command == 'patterns':
                self.show_file_patterns()
            elif command.startswith('analyze'):
                try:
                    sample_key = command.split()[1]
                    self.analyze_sample(sample_key)
                except IndexError:
                    print("Usage: analyze <sample_key>")
            elif command.startswith('roi'):
                try:
                    sample_key = command.split()[1]
                    self.analyze_roi_files(sample_key)
                except IndexError:
                    print("Usage: roi <sample_key>")
            elif command.startswith('list'):
                parts = command.split()
                celltype_filter = parts[1] if len(parts) > 1 else None
                self.list_samples(celltype_filter)
            else:
                print("Unknown command. Type 'quit' to exit.")
    
    def analyze_sample(self, sample_key):
        """Analyze a specific sample in detail"""
        if sample_key not in self.samples:
            print(f"Sample {sample_key} not found!")
            return
        
        sample_info = self.samples[sample_key]
        print(f"\nDetailed analysis for {sample_key}:")
        print(f"  Cell type: {sample_info['celltype']}")
        print(f"  Sample ID: {sample_info['sample_id']}")
        print(f"  Path: {sample_info['path']}")
        
        print(f"\n  DAPI file:")
        if sample_info['dapi_file']:
            print(f"    ✓ {sample_info['dapi_file'].name}")
            print(f"      Size: {sample_info['dapi_file'].stat().st_size:,} bytes")
        else:
            print(f"    ✗ Not found")
        
        print(f"\n  Marker file:")
        if sample_info['marker_file']:
            print(f"    ✓ {sample_info['marker_file'].name}")
            print(f"      Size: {sample_info['marker_file'].stat().st_size:,} bytes")
        else:
            print(f"    ✗ Not found")
        
        print(f"\n  ROI files ({len(sample_info['roi_files'])}):")
        if sample_info['roi_files']:
            for roi_file in sample_info['roi_files']:
                print(f"    ✓ {roi_file.name}")
                print(f"      Size: {roi_file.stat().st_size:,} bytes")
        else:
            print(f"    ✗ None found")
        
        if sample_info['other_files']:
            print(f"\n  Other files ({len(sample_info['other_files'])}):")
            for other_file in sample_info['other_files']:
                print(f"    - {other_file.name}")
    
    def list_samples(self, celltype_filter=None):
        """List all samples, optionally filtered by celltype"""
        print(f"\nSample list" + (f" (filtered by {celltype_filter})" if celltype_filter else "") + ":")
        
        count = 0
        for sample_key in sorted(self.samples.keys()):
            sample_info = self.samples[sample_key]
            
            if celltype_filter and sample_info['celltype'] != celltype_filter:
                continue
            
            count += 1
            has_dapi = "✓" if sample_info['dapi_file'] else "✗"
            has_marker = "✓" if sample_info['marker_file'] else "✗"
            has_roi = "✓" if sample_info['roi_files'] else "✗"
            
            print(f"  {count:3d}. {sample_key:<20} DAPI:{has_dapi} Marker:{has_marker} ROI:{has_roi}")
        
        print(f"\nTotal: {count} samples")

def main():
    """Main function"""
    analyzer = ScribblesAnalyzer("scribbles")
    
    if not analyzer.samples:
        print("No samples found. Please check the scribbles directory structure.")
        return
    
    # Show initial summary
    analyzer.create_summary_report()
    analyzer.list_problematic_samples()
    
    # Start interactive explorer
    analyzer.interactive_explorer()

if __name__ == "__main__":
    main()
