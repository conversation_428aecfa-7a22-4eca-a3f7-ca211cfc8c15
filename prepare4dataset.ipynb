import importlib
import ifimage_tools
importlib.reload(ifimage_tools)
import pandas as pd
import warnings


import os
import numpy as np
from stardist.matching import matching
from ifimage_tools import IfImageDataset
dataset = IfImageDataset(
    image_dir='processed_dataset/images',
    nuclei_masks_dir='processed_dataset/masks',
    cell_masks_dir='processed_dataset/masks',
    manual_cell_counts={}
)

dataset.load_data()

i=0
for sample_id, sample in dataset.samples.items():
    if sample.dapi_multi_mask is None:
        i= i +1
i

