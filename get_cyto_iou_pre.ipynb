{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bf00edf62f536d2d", "metadata": {"ExecuteTime": {"end_time": "2025-03-17T07:53:12.422247Z", "start_time": "2025-03-17T07:53:12.410927Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Welcome to CellposeSAM, cellpose v\n", "cellpose version: \t4.0.4 \n", "platform:       \tlinux \n", "python version: \t3.10.16 \n", "torch version:  \t2.7.0+cu126! The neural network component of\n", "CPSAM is much larger than in previous versions and CPU excution is slow. \n", "We encourage users to use GPU/MPS if available. \n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "A module that was compiled using NumPy 1.x cannot be run in\n", "NumPy 2.2.6 as it may crash. To support both 1.x and 2.x\n", "versions of NumPy, modules must be compiled with NumPy 2.0.\n", "Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n", "\n", "If you are a user of the module, the easiest solution will be to\n", "downgrade to 'numpy<2' or try to upgrade the affected module.\n", "We expect that some modules will need time to support NumPy 2.\n", "\n", "Traceback (most recent call last):  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 199, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3077, in run_cell\n", "    result = self._run_cell(\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3132, in _run_cell\n", "    result = runner(coro)\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3336, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3519, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3579, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/tmp/ipykernel_2677219/3720144104.py\", line 2, in <module>\n", "    import ifimage_tools\n", "  File \"/home/<USER>/ifimage/ifimage_tools.py\", line 8, in <module>\n", "    from stardist.matching import matching\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/stardist/__init__.py\", line 15, in <module>\n", "    from .geometry import star_dist,   polygons_to_label,   relabel_image_stardist, ray_angles, dist_to_coord\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/stardist/geometry/__init__.py\", line 5, in <module>\n", "    from .geom2d import star_dist, relabel_image_stardist, ray_angles, dist_to_coord, polygons_to_label, polygons_to_label_coord\n", "  File \"/home/<USER>/miniconda3/envs/ifimage/lib/python3.10/site-packages/stardist/geometry/geom2d.py\", line 11, in <module>\n", "    from ..lib.stardist2d import c_star_dist\n"]}, {"ename": "AttributeError", "evalue": "_ARRAY_API not found", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;31mAttributeError\u001b[0m: _ARRAY_API not found"]}, {"ename": "ImportError", "evalue": "numpy.core.multiarray failed to import", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[1], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mimportlib\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mifimage_tools\u001b[39;00m\n\u001b[1;32m      3\u001b[0m importlib\u001b[38;5;241m.\u001b[39mreload(ifimage_tools)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpd\u001b[39;00m\n", "File \u001b[0;32m~/ifimage/ifimage_tools.py:8\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mscipy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mndimage\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m distance_transform_edt\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mskimage\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msegmentation\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m watershed\n\u001b[0;32m----> 8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mstardist\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmatching\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m matching\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mImageSample\u001b[39;00m:\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, sample_id, manual_cell_count\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/stardist/__init__.py:15\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnms\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m non_maximum_suppression, non_maximum_suppression_3d, non_maximum_suppression_3d_sparse\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m edt_prob, fill_label_holes, sample_points, calculate_extents, export_imagej_rois, gputools_available\n\u001b[0;32m---> 15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgeometry\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m star_dist,   polygons_to_label,   relabel_image_stardist, ray_angles, dist_to_coord\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgeometry\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m star_dist3D, polyhedron_to_label, relabel_image_stardist3D\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mplot\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m random_label_cmap, draw_polygons, _draw_polygons\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/stardist/geometry/__init__.py:5\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m__future__\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m absolute_import, print_function\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# TODO: rethink naming for 2D/3D functions\u001b[39;00m\n\u001b[0;32m----> 5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgeom2d\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m star_dist, relabel_image_stardist, ray_angles, dist_to_coord, polygons_to_label, polygons_to_label_coord\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgeom3d\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m star_dist3D, polyhedron_to_label, relabel_image_stardist3D, dist_to_coord3D, export_to_obj_file3D\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgeom2d\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m _dist_to_coord_old, _polygons_to_label_old\n", "File \u001b[0;32m~/miniconda3/envs/ifimage/lib/python3.10/site-packages/stardist/geometry/geom2d.py:11\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m path_absolute, _is_power_of_2, _normalize_grid\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmatching\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m _check_label_array\n\u001b[0;32m---> 11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mstardist2d\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m c_star_dist\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_ocl_star_dist\u001b[39m(lbl, n_rays\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m32\u001b[39m, grid\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m1\u001b[39m,\u001b[38;5;241m1\u001b[39m)):\n\u001b[1;32m     16\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mgputools\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m OCLProgram, OCLArray, OCLImage\n", "\u001b[0;31mImportError\u001b[0m: numpy.core.multiarray failed to import"]}], "source": ["import importlib\n", "import ifimage_tools\n", "importlib.reload(ifimage_tools)\n", "import pandas as pd\n", "import warnings"]}, {"cell_type": "code", "execution_count": 2, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-03-16T08:48:49.039617Z", "start_time": "2025-03-16T08:48:40.774598Z"}, "collapsed": true}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "image_dir = \"Reorgnized Ground Truth\"\n", "masks_dir = \"Reorgnized Ground Truth/mask\"\n", "dataset = ifimage_tools.IfImageDataset(image_dir, masks_dir, {})\n", "dataset.load_data()\n", "old_version_sample_ids = [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]\n", "for sample_id in old_version_sample_ids:\n", "    if sample_id in dataset.samples:\n", "        del dataset.samples[sample_id]\n", "METHODS = [\"cyto3\", \"watershed\", \"cell_expansion\"]\n", "iou_thresholds = np.arange(0.5, 1.0, 0.05)\n", "save_dir = \"pre_iou\"\n", "os.makedirs(save_dir, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "8de8e5d4b6a5b4a8", "metadata": {"ExecuteTime": {"end_time": "2025-03-17T07:40:53.806293Z", "start_time": "2025-03-17T07:40:51.990046Z"}}, "outputs": [], "source": ["import joblib\n", "with open(\"dataset_after_pipeline.joblib\", \"rb\") as f:\n", "    dataset = joblib.load(f)"]}, {"cell_type": "markdown", "id": "e70532ec00217542", "metadata": {}, "source": ["# Get ps iou plot"]}, {"cell_type": "code", "execution_count": null, "id": "5282f1adeb35d831", "metadata": {"ExecuteTime": {"end_time": "2025-03-17T15:11:52.529755Z", "start_time": "2025-03-17T15:11:16.243135Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'dataset' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 13\u001b[0m\n\u001b[1;32m     10\u001b[0m METHODS \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcellpose\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwatershed\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcell_expansion\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m     11\u001b[0m all_precisions \u001b[38;5;241m=\u001b[39m {method: {thr: [] \u001b[38;5;28;01mfor\u001b[39;00m thr \u001b[38;5;129;01min\u001b[39;00m iou_thresholds} \u001b[38;5;28;01mfor\u001b[39;00m method \u001b[38;5;129;01min\u001b[39;00m METHODS}\n\u001b[0;32m---> 13\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m sample_id, sample \u001b[38;5;129;01min\u001b[39;00m \u001b[43mdataset\u001b[49m\u001b[38;5;241m.\u001b[39msamples\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     14\u001b[0m     gt_mask \u001b[38;5;241m=\u001b[39m sample\u001b[38;5;241m.\u001b[39mcellbodies_multimask\n\u001b[1;32m     15\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m method \u001b[38;5;129;01min\u001b[39;00m METHODS:\n", "\u001b[0;31mNameError\u001b[0m: name 'dataset' is not defined"]}], "source": ["import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "#iou_thresholds = np.arange(0.05, 1.0, 0.05)\n", "iou_thresholds = np.arange(0.5, 1.0, 0.05)\n", "# plt.style.use('ggplot')\n", "\n", "# Define the evaluation methods\n", "METHODS = [\"cellpose\", \"watershed\", \"cell_expansion\"]\n", "all_precisions = {method: {thr: [] for thr in iou_thresholds} for method in METHODS}\n", "\n", "for sample_id, sample in dataset.samples.items():\n", "    gt_mask = sample.cellbodies_multimask\n", "    for method in METHODS:\n", "        if method not in sample.cyto_positive_masks:\n", "            print(f\"Sample {sample_id} lacks prediction for method {method}; skipping...\")\n", "            continue\n", "        pred_mask_source = sample.cyto_positive_masks[method]\n", "        try:\n", "            if isinstance(pred_mask_source, np.ndarray):\n", "                pred_mask = pred_mask_source\n", "            else:\n", "                \n", "\n", "                if not os.path.exists(pred_mask_source):\n", "                    print(f\"Prediction file does not exist: {pred_mask_source} (sample {sample_id}, method {method}); skipping...\")\n", "                    continue\n", "                pred_mask = np.load(pred_mask_source)\n", "        except Exception as e:\n", "            print(f\"Error loading prediction mask (sample {sample_id}, method {method}): {e}; skipping...\")\n", "            continue\n", "\n", "        precisions = []\n", "        for thr in iou_thresholds:\n", "            try:\n", "                match = matching(gt_mask, pred_mask, thresh=thr)\n", "                precisions.append(match.precision)\n", "            except Exception as e:\n", "                print(f\"Error computing matching (sample {sample_id}, method {method}, IoU {thr}): {e}\")\n", "                precisions.append(0)\n", "\n", "        if all(p == 0 for p in precisions):\n", "            print(f\"All precision values are 0 for sample {sample_id} with method {method}; skipping...\")\n", "            continue\n", "\n", "        for thr, prec in zip(iou_thresholds, precisions):\n", "            all_precisions[method][thr].append(prec)\n", "\n", "legend_mapping = {\n", "    \"cellpose\": \"CellPose\",\n", "    \"watershed\": \"Watershed\",\n", "    \"cell_expansion\": \"Cell Expansion\"\n", "}\n", "\n", "# Plot mean precision vs. IoU threshold for each method and compute mAP\n", "plt.figure(figsize=(6, 4))\n", "for method in METHODS:\n", "    valid_thresholds = [thr for thr in iou_thresholds if all_precisions[method][thr]]\n", "    if not valid_thresholds:\n", "        continue\n", "\n", "    # Compute the mean precision at each valid threshold\n", "    mean_precisions = {thr: np.mean(all_precisions[method][thr]) for thr in valid_thresholds}\n", "\n", "    # Compute mAP as the average of the mean precisions\n", "    mAP = np.mean(list(mean_precisions.values()))\n", "\n", "    # Update the label to include mAP value (formatted to 2 decimal places)\n", "    label_text = f\"{legend_mapping.get(method, method)} (mAP={mAP:.2f})\"\n", "\n", "    plt.plot(\n", "        valid_thresholds,\n", "        [mean_precisions[thr] for thr in valid_thresholds],\n", "        marker='o',\n", "        linestyle='-',\n", "        label=label_text\n", "    )\n", "\n", "plt.title(\"Mean Precision vs. IoU Threshold\")\n", "plt.xlabel(\"IoU Threshold\")\n", "plt.ylabel(\"Mean Precision\")\n", "plt.legend()\n", "plt.tight_layout()\n", "#plt.savefig(\"mean_precision_vs_iou_v1.pdf\", format='pdf', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 10, "id": "43e154c41584747e", "metadata": {"ExecuteTime": {"end_time": "2025-03-17T07:43:48.669769Z", "start_time": "2025-03-17T07:42:40.660787Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error computing matching (sample 6390v2, method cellpose, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cellpose, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 6390v2 with method cellpose; skipping...\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method watershed, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 6390v2 with method watershed; skipping...\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 6390v2, method cell_expansion, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 6390v2 with method cell_expansion; skipping...\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cellpose, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8408jan22 with method cellpose; skipping...\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method watershed, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8408jan22 with method watershed; skipping...\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8408jan22, method cell_expansion, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8408jan22 with method cell_expansion; skipping...\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cellpose, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8406jan22 with method cellpose; skipping...\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method watershed, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8406jan22 with method watershed; skipping...\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8406jan22, method cell_expansion, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8406jan22 with method cell_expansion; skipping...\n", "All precision values are 0 for sample 4201 with method cell_expansion; skipping...\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cellpose, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8405v2 with method cellpose; skipping...\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method watershed, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8405v2 with method watershed; skipping...\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405v2, method cell_expansion, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8405v2 with method cell_expansion; skipping...\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cellpose, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8405jan22 with method cellpose; skipping...\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method watershed, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8405jan22 with method watershed; skipping...\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8405jan22, method cell_expansion, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8405jan22 with method cell_expansion; skipping...\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cellpose, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8407jan22 with method cellpose; skipping...\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method watershed, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8407jan22 with method watershed; skipping...\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.05): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.1): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.15000000000000002): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.2): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.25): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.3): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.35000000000000003): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.4): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.45): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.5): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.55): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.6000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.6500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.7000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.7500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.8): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.8500000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.9000000000000001): y_true must be an array of non-negative integers.\n", "Error computing matching (sample 8407jan22, method cell_expansion, IoU 0.9500000000000001): y_true must be an array of non-negative integers.\n", "All precision values are 0 for sample 8407jan22 with method cell_expansion; skipping...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "iou_thresholds = np.arange(0.05, 1.0, 0.05)\n", "# iou_thresholds = np.arange(0.5, 1.0, 0.05)\n", "plt.style.use('ggplot')\n", "\n", "# Define the evaluation methods\n", "METHODS = [\"cellpose\", \"watershed\", \"cell_expansion\"]\n", "all_precisions = {method: {thr: [] for thr in iou_thresholds} for method in METHODS}\n", "\n", "# Iterate over each sample in the dataset\n", "for sample_id, sample in dataset.samples.items():\n", "    gt_mask = sample.cellbodies_multimask\n", "    for method in METHODS:\n", "        if method not in sample.cyto_positive_masks:\n", "            print(f\"Sample {sample_id} lacks prediction for method {method}; skipping...\")\n", "            continue\n", "        pred_mask_source = sample.cyto_positive_masks[method]\n", "        try:\n", "            if isinstance(pred_mask_source, np.ndarray):\n", "                pred_mask = pred_mask_source\n", "            else:\n", "                if not os.path.exists(pred_mask_source):\n", "                    print(f\"Prediction file does not exist: {pred_mask_source} (sample {sample_id}, method {method}); skipping...\")\n", "                    continue\n", "                pred_mask = np.load(pred_mask_source)\n", "        except Exception as e:\n", "            print(f\"Error loading prediction mask (sample {sample_id}, method {method}): {e}; skipping...\")\n", "            continue\n", "\n", "        precisions = []\n", "        for thr in iou_thresholds:\n", "            try:\n", "                match = matching(gt_mask, pred_mask, thresh=thr)\n", "                precisions.append(match.precision)\n", "            except Exception as e:\n", "                print(f\"Error computing matching (sample {sample_id}, method {method}, IoU {thr}): {e}\")\n", "                precisions.append(0)\n", "\n", "        if all(p == 0 for p in precisions):\n", "            print(f\"All precision values are 0 for sample {sample_id} with method {method}; skipping...\")\n", "            continue\n", "\n", "        for thr, prec in zip(iou_thresholds, precisions):\n", "            all_precisions[method][thr].append(prec)\n", "\n", "legend_mapping = {\n", "    \"cellpose\": \"CellPose\",\n", "    \"watershed\": \"Watershed\",\n", "    \"cell_expansion\": \"Cell Expansion\"\n", "}\n", "\n", "# Plot mean precision vs. IoU threshold for each method and compute mAP\n", "plt.figure(figsize=(6, 4))\n", "for method in METHODS:\n", "    valid_thresholds = [thr for thr in iou_thresholds if all_precisions[method][thr]]\n", "    if not valid_thresholds:\n", "        continue\n", "\n", "    # Compute the mean precision at each valid threshold\n", "    mean_precisions = {thr: np.mean(all_precisions[method][thr]) for thr in valid_thresholds}\n", "\n", "    # Compute mAP as the average of the mean precisions\n", "    mAP = np.mean(list(mean_precisions.values()))\n", "\n", "    # Update the label to include mAP value (formatted to 2 decimal places)\n", "    label_text = f\"{legend_mapping.get(method, method)} (mAP={mAP:.2f})\"\n", "\n", "    plt.plot(\n", "        valid_thresholds,\n", "        [mean_precisions[thr] for thr in valid_thresholds],\n", "        marker='o',\n", "        linestyle='-',\n", "        label=label_text\n", "    )\n", "\n", "plt.title(\"Mean Precision vs. IoU Threshold\")\n", "plt.xlabel(\"IoU Threshold\")\n", "plt.ylabel(\"Mean Precision\")\n", "plt.legend()\n", "plt.tight_layout()\n", "#plt.savefig(\"mean_precision_vs_iou_v2.pdf\", format='pdf', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "4c35f45c99435f36", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ifimage", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}