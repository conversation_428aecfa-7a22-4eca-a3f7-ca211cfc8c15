{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ab1ca867", "metadata": {}, "outputs": [], "source": ["import importlib\n", "import ifimage_tools\n", "import matplotlib.pyplot as plt\n", "import importlib\n", "import ifimage_tools\n", "import matplotlib.pyplot as plt\n", "importlib.reload(ifimage_tools)\n", "from ifimage_tools import *\n", "import pandas as pd\n", "import os\n", "from itertools import islice\n", "import numpy as np\n", "from stardist.matching import matching\n", "import cv2\n", "import pickle"]}, {"cell_type": "code", "execution_count": null, "id": "83544658", "metadata": {}, "outputs": [], "source": ["with open(\"dataset.pkl\", \"rb\") as f:\n", "    dataset = pickle.load(f)\n", "print(\"Dataset loaded from dataset.pkl\")\n", "id= pd.read_csv(\"/Users/<USER>/PycharmProjects/ifimage/best_annotated_images/best_samples_report.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "6d3b0232", "metadata": {}, "outputs": [], "source": ["dataset.samples.items()\n"]}, {"cell_type": "code", "execution_count": 4, "id": "514ea880", "metadata": {}, "outputs": [], "source": ["# for i in range(len(id)):\n", "#     sample=dataset.samples[f\"{id.iloc[i,1]}\"]\n", "#     draw_overlap(sample.cellbodies_mask,sample.cyto_positive_masks[\"cellpose2chan\"],\n", "#                  title=f\"ID:{id.iloc[i,1]} {id.iloc[i,0].upper()} mAP={id.iloc[i,2]:.3f}\",\n", "#                  display_legend=True)"]}, {"cell_type": "code", "execution_count": null, "id": "fffea9e0", "metadata": {"notebookRunGroups": {"groupValue": "2"}}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import cv2\n", "import os\n", "from stardist.matching import matching\n", "\n", "info = pd.read_csv(\"/Users/<USER>/PycharmProjects/ifimage/best_annotated_images/best_samples_report.csv\")\n", "\n", "fig, axs = plt.subplots(len(info), 4, figsize=(16, 4 * len(info)))\n", "\n", "for i in range(len(info)):\n", "    sample_id = info.iloc[i,1]\n", "    sample = dataset.samples[str(sample_id)]\n", "    celltype = sample.celltype\n", "    \n", "    cell_body_ann = sample.cellbodies_mask\n", "    cell_body_pred = sample.cyto_positive_masks[\"cellpose2chan\"]\n", "    nuclei_ann = sample.dapi_multi_mask\n", "    nuclei_pred = sample.masks[\"cyto3\"]\n", "    \n", "    # Calculate IoU using stardist.matching\n", "    iou_body = calculate_overall_iou(cell_body_ann, cell_body_pred)\n", "    iou_nuclei = calculate_overall_iou(nuclei_ann, nuclei_pred)\n", "\n", "    # Display images with celltype and sample_id labels\n", "    # Show marker image in green channel\n", "    axs[i, 0].imshow(sample.marker, cmap='Greens')\n", "    axs[i, 0].axis('off')\n", "    axs[i, 0].text(5, 20, f\"{celltype} {sample_id}\", color='white', fontsize=10, backgroundcolor='black')\n", "    \n", "    axs[i, 1].imshow(cell_body_pred, cmap=\"plasma\")\n", "    axs[i, 1].axis('off')\n", "    axs[i, 1].text(5, 20, f\"IoU: {iou_body:.2f}\", color='white', fontsize=10, backgroundcolor='black')\n", "    \n", "    # Show dapi image in blue channel\n", "    axs[i, 2].imshow(sample.dapi, cmap='Blues')\n", "    axs[i, 2].axis('off')\n", "    \n", "    axs[i, 3].imshow(nuclei_pred, cmap='plasma')\n", "    axs[i, 3].axis('off')\n", "    axs[i, 3].text(5, 20, f\"IoU: {iou_nuclei:.2f}\", color='white', fontsize=10, backgroundcolor='black')\n", "\n", "col_titles = ['Cell Body: Annotation', 'Prediction', 'Nuclei: Annotation', 'Prediction']\n", "for ax, col in zip(axs[0], col_titles):\n", "    ax.set_title(col, fontsize=14)\n", "\n", "for ax, row in zip(axs[:, 0], info.iloc[:,0]):\n", "    ax.set_ylabel(row.capitalize(), fontsize=14)\n", "\n", "plt.tight_layout()\n", "plt.savefig(\"summary_panel.pdf\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "223a42dc", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import cv2\n", "import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "\n", "info = pd.read_csv(\"/Users/<USER>/PycharmProjects/ifimage/best_annotated_images/best_samples_report.csv\")\n", "\n", "# Create figure with number of rows matching comparing_ids length\n", "comparing_ids=[\"15972\",\"1120\",\"12779\",\"8406jan22\",\"4201\"]\n", "fig, axs = plt.subplots(len(comparing_ids), 4, figsize=(16, 4 * len(comparing_ids)))\n", "\n", "# Use enumerate to get integer indices for axs access\n", "for idx, sample_id in enumerate(comparing_ids):\n", "    sample = dataset.samples[sample_id]\n", "    celltype = sample.celltype\n", "    \n", "    cell_body_ann = sample.cellbodies_multimask\n", "    cell_body_pred = sample.cyto_positive_masks[\"cellpose2chan\"]\n", "    nuclei_ann = sample.dapi_multi_mask\n", "    nuclei_pred = sample.masks[\"cyto3\"]\n", "    \n", "    # Calculate IoU using stardist.matching\n", "    iou_body = calculate_overall_iou(cell_body_ann, cell_body_pred)\n", "    iou_nuclei = calculate_overall_iou(nuclei_ann, nuclei_pred)\n", "\n", "    # Display images with celltype and sample_id labels\n", "    # Show marker image in green channel\n", "    axs[idx, 0].imshow(sample.cellbodies_multimask, \n", "                       #cmap='Greens'\n", "                       )\n", "    axs[idx, 0].axis('off')\n", "    axs[idx, 0].text(5, 20, f\"{celltype} {sample_id}\", color='white', fontsize=10, backgroundcolor='black')\n", "    \n", "    axs[idx, 1].imshow(cell_body_pred, cmap=\"plasma\")\n", "    axs[idx, 1].axis('off')\n", "    axs[idx, 1].text(5, 20, f\"IoU: {iou_body:.2f}\", color='white', fontsize=10, backgroundcolor='black')\n", "    \n", "    # Show dapi image in blue channel\n", "    axs[idx, 2].imshow(sample.dapi, cmap='Blues')\n", "    axs[idx, 2].axis('off')\n", "    \n", "    # Convert nuclei_pred to float if it's object dtype\n", "    if nuclei_pred.dtype == np.object_:\n", "        nuclei_pred = nuclei_pred.astype(np.float32)\n", "    axs[idx, 3].imshow(nuclei_pred, cmap='plasma')\n", "    axs[idx, 3].axis('off')\n", "    axs[idx, 3].text(5, 20, f\"IoU: {iou_nuclei:.2f}\", color='white', fontsize=10, backgroundcolor='black')\n", "\n", "col_titles = ['Cell Body: Annotation', 'Prediction', 'Nuclei: Annotation', 'Prediction']\n", "for ax, col in zip(axs[0], col_titles):\n", "    ax.set_title(col, fontsize=14)\n", "\n", "plt.tight_layout()\n", "plt.savefig(\"summary_panel.pdf\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 3, "id": "73dab3b3", "metadata": {}, "outputs": [], "source": ["from preprocessing import *"]}, {"cell_type": "code", "execution_count": 7, "id": "fc13db6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ROI '0013-0263.roi' drawn with label 1.\n", "ROI '0011-0070.roi' drawn with label 2.\n", "ROI '0007-0250.roi' drawn with label 3.\n", "ROI '0025-0212.roi' drawn with label 4.\n", "ROI '0038-0207.roi' drawn with label 5.\n", "ROI '0056-0096.roi' drawn with label 6.\n", "ROI '0077-0112.roi' drawn with label 7.\n", "ROI '0088-0108.roi' drawn with label 8.\n", "ROI '0089-0130.roi' drawn with label 9.\n", "ROI '0098-0156.roi' drawn with label 10.\n", "ROI '0108-0127.roi' drawn with label 11.\n", "ROI '0108-0069.roi' drawn with label 12.\n", "ROI '0127-0081.roi' drawn with label 13.\n", "ROI '0107-0047.roi' drawn with label 14.\n", "ROI '0122-0052.roi' drawn with label 15.\n", "ROI '0133-0069.roi' drawn with label 16.\n", "ROI '0139-0077.roi' drawn with label 17.\n", "ROI '0146-0109.roi' drawn with label 18.\n", "ROI '0242-0018.roi' drawn with label 19.\n", "ROI '0257-0046.roi' drawn with label 20.\n", "ROI '0289-0047.roi' drawn with label 21.\n", "ROI '0296-0029.roi' drawn with label 22.\n", "ROI '0336-0019.roi' drawn with label 23.\n", "ROI '0366-0031.roi' drawn with label 24.\n", "ROI '0376-0011.roi' drawn with label 25.\n", "ROI '0387-0054.roi' drawn with label 26.\n", "ROI '0414-0071.roi' drawn with label 27.\n", "ROI '0432-0039.roi' drawn with label 28.\n", "ROI '0436-0006.roi' drawn with label 29.\n", "ROI '0449-0007.roi' drawn with label 30.\n", "ROI '0459-0045.roi' drawn with label 31.\n", "ROI '0503-0010.roi' drawn with label 32.\n", "ROI '0485-0073.roi' drawn with label 33.\n", "ROI '0512-0057.roi' drawn with label 34.\n", "ROI '0498-0094.roi' drawn with label 35.\n", "ROI '0383-0126.roi' drawn with label 36.\n", "ROI '0347-0129.roi' drawn with label 37.\n", "ROI '0330-0120.roi' drawn with label 38.\n", "ROI '0314-0124.roi' drawn with label 39.\n", "ROI '0218-0103.roi' drawn with label 40.\n", "ROI '0232-0120.roi' drawn with label 41.\n", "ROI '0214-0129.roi' drawn with label 42.\n", "ROI '0175-0136.roi' drawn with label 43.\n", "ROI '0147-0162.roi' drawn with label 44.\n", "ROI '0192-0157.roi' drawn with label 45.\n", "ROI '0214-0156.roi' drawn with label 46.\n", "ROI '0245-0183.roi' drawn with label 47.\n", "ROI '0238-0198.roi' drawn with label 48.\n", "ROI '0273-0206.roi' drawn with label 49.\n", "ROI '0251-0231.roi' drawn with label 50.\n", "ROI '0285-0253.roi' drawn with label 51.\n", "ROI '0284-0267.roi' drawn with label 52.\n", "ROI '0335-0232.roi' drawn with label 53.\n", "ROI '0386-0182.roi' drawn with label 54.\n", "ROI '0407-0170.roi' drawn with label 55.\n", "ROI '0425-0215.roi' drawn with label 56.\n", "ROI '0421-0191.roi' drawn with label 57.\n", "ROI '0436-0175.roi' drawn with label 58.\n", "ROI '0497-0165.roi' drawn with label 59.\n", "ROI '0512-0177.roi' drawn with label 60.\n", "ROI '0477-0191.roi' drawn with label 61.\n", "ROI '0481-0212.roi' drawn with label 62.\n", "ROI '0500-0215.roi' drawn with label 63.\n", "ROI '0488-0229.roi' drawn with label 64.\n", "ROI '0470-0237.roi' drawn with label 65.\n", "ROI '0425-0307.roi' drawn with label 66.\n", "ROI '0452-0305.roi' drawn with label 67.\n", "ROI '0452-0365.roi' drawn with label 68.\n", "ROI '0491-0366.roi' drawn with label 69.\n", "ROI '0377-0378.roi' drawn with label 70.\n", "ROI '0281-0366.roi' drawn with label 71.\n", "ROI '0148-0279.roi' drawn with label 72.\n", "ROI '0086-0214.roi' drawn with label 73.\n", "ROI '0081-0226.roi' drawn with label 74.\n", "ROI '0076-0241.roi' drawn with label 75.\n", "ROI '0036-0229.roi' drawn with label 76.\n", "ROI '0052-0230.roi' drawn with label 77.\n", "ROI '0013-0354.roi' drawn with label 78.\n", "ROI '0055-0281.roi' drawn with label 79.\n", "ROI '0056-0303.roi' drawn with label 80.\n", "ROI '0064-0324.roi' drawn with label 81.\n", "ROI '0034-0347.roi' drawn with label 82.\n", "ROI '0047-0351.roi' drawn with label 83.\n", "ROI '0004-0436.roi' drawn with label 84.\n", "ROI '0041-0388.roi' drawn with label 85.\n", "ROI '0065-0408.roi' drawn with label 86.\n", "ROI '0085-0416.roi' drawn with label 87.\n", "ROI '0076-0392.roi' drawn with label 88.\n", "ROI '0076-0368.roi' drawn with label 89.\n", "ROI '0088-0364.roi' drawn with label 90.\n", "ROI '0094-0343.roi' drawn with label 91.\n", "ROI '0103-0314.roi' drawn with label 92.\n", "ROI '0111-0350.roi' drawn with label 93.\n", "ROI '0151-0370.roi' drawn with label 94.\n", "ROI '0164-0351.roi' drawn with label 95.\n", "ROI '0089-0448.roi' drawn with label 96.\n", "ROI '0115-0460.roi' drawn with label 97.\n", "ROI '0063-0476.roi' drawn with label 98.\n", "ROI '0023-0458.roi' drawn with label 99.\n", "ROI '0011-0476.roi' drawn with label 100.\n", "ROI '0017-0486.roi' drawn with label 101.\n", "ROI '0014-0567.roi' drawn with label 102.\n", "ROI '0023-0536.roi' drawn with label 103.\n", "ROI '0039-0532.roi' drawn with label 104.\n", "ROI '0066-0546.roi' drawn with label 105.\n", "ROI '0063-0580.roi' drawn with label 106.\n", "ROI '0061-0632.roi' drawn with label 107.\n", "ROI '0017-0674.roi' drawn with label 108.\n", "ROI '0094-0601.roi' drawn with label 109.\n", "ROI '0105-0592.roi' drawn with label 110.\n", "ROI '0107-0572.roi' drawn with label 111.\n", "ROI '0088-0518.roi' drawn with label 112.\n", "ROI '0114-0493.roi' drawn with label 113.\n", "ROI '0121-0505.roi' drawn with label 114.\n", "ROI '0109-0395.roi' drawn with label 115.\n", "ROI '0223-0421.roi' drawn with label 116.\n", "ROI '0160-0515.roi' drawn with label 117.\n", "ROI '0171-0501.roi' drawn with label 118.\n", "ROI '0185-0498.roi' drawn with label 119.\n", "ROI '0187-0530.roi' drawn with label 120.\n", "ROI '0198-0575.roi' drawn with label 121.\n", "ROI '0221-0559.roi' drawn with label 122.\n", "ROI '0229-0509.roi' drawn with label 123.\n", "ROI '0208-0474.roi' drawn with label 124.\n", "ROI '0240-0443.roi' drawn with label 125.\n", "ROI '0250-0486.roi' drawn with label 126.\n", "ROI '0260-0515.roi' drawn with label 127.\n", "ROI '0276-0514.roi' drawn with label 128.\n", "ROI '0271-0534.roi' drawn with label 129.\n", "ROI '0300-0543.roi' drawn with label 130.\n", "ROI '0243-0599.roi' drawn with label 131.\n", "ROI '0252-0611.roi' drawn with label 132.\n", "ROI '0254-0594.roi' drawn with label 133.\n", "ROI '0270-0592.roi' drawn with label 134.\n", "ROI '0122-0619.roi' drawn with label 135.\n", "ROI '0128-0647.roi' drawn with label 136.\n", "ROI '0173-0683.roi' drawn with label 137.\n", "ROI '0196-0664.roi' drawn with label 138.\n", "ROI '0226-0688.roi' drawn with label 139.\n", "ROI '0282-0654.roi' drawn with label 140.\n", "ROI '0290-0633.roi' drawn with label 141.\n", "ROI '0299-0655.roi' drawn with label 142.\n", "ROI '0307-0641.roi' drawn with label 143.\n", "ROI '0336-0586.roi' drawn with label 144.\n", "ROI '0346-0550.roi' drawn with label 145.\n", "ROI '0362-0630.roi' drawn with label 146.\n", "ROI '0372-0620.roi' drawn with label 147.\n", "ROI '0370-0640.roi' drawn with label 148.\n", "ROI '0371-0647.roi' drawn with label 149.\n", "ROI '0383-0642.roi' drawn with label 150.\n", "ROI '0446-0606.roi' drawn with label 151.\n", "ROI '0399-0564.roi' drawn with label 152.\n", "ROI '0390-0565.roi' drawn with label 153.\n", "ROI '0376-0579.roi' drawn with label 154.\n", "ROI '0378-0559.roi' drawn with label 155.\n", "ROI '0382-0542.roi' drawn with label 156.\n", "ROI '0340-0447.roi' drawn with label 157.\n", "ROI '0389-0512.roi' drawn with label 158.\n", "ROI '0394-0525.roi' drawn with label 159.\n", "ROI '0443-0552.roi' drawn with label 160.\n", "ROI '0466-0553.roi' drawn with label 161.\n", "ROI '0420-0405.roi' drawn with label 162.\n", "ROI '0437-0455.roi' drawn with label 163.\n", "ROI '0466-0425.roi' drawn with label 164.\n", "ROI '0464-0467.roi' drawn with label 165.\n", "ROI '0502-0405.roi' drawn with label 166.\n", "ROI '0476-0481.roi' drawn with label 167.\n", "ROI '0484-0506.roi' drawn with label 168.\n", "ROI '0499-0499.roi' drawn with label 169.\n", "ROI '0496-0485.roi' drawn with label 170.\n", "ROI '0506-0473.roi' drawn with label 171.\n", "ROI '0499-0564.roi' drawn with label 172.\n", "ROI '0468-0683.roi' drawn with label 173.\n", "ROI '0490-0675.roi' drawn with label 174.\n", "ROI '0510-0649.roi' drawn with label 175.\n", "ROI '0514-0673.roi' drawn with label 176.\n", "ROI '0536-0681.roi' drawn with label 177.\n", "ROI '0522-0688.roi' drawn with label 178.\n", "ROI '0530-0671.roi' drawn with label 179.\n", "ROI '0500-0689.roi' drawn with label 180.\n", "ROI '0551-0565.roi' drawn with label 181.\n", "ROI '0539-0551.roi' drawn with label 182.\n", "ROI '0555-0534.roi' drawn with label 183.\n", "ROI '0560-0550.roi' drawn with label 184.\n", "ROI '0571-0561.roi' drawn with label 185.\n", "ROI '0565-0578.roi' drawn with label 186.\n", "ROI '0588-0602.roi' drawn with label 187.\n", "ROI '0600-0583.roi' drawn with label 188.\n", "ROI '0592-0516.roi' drawn with label 189.\n", "ROI '0583-0470.roi' drawn with label 190.\n", "ROI '0583-0448.roi' drawn with label 191.\n", "ROI '0566-0436.roi' drawn with label 192.\n", "ROI '0548-0413.roi' drawn with label 193.\n", "ROI '0542-0359.roi' drawn with label 194.\n", "ROI '0574-0366.roi' drawn with label 195.\n", "ROI '0611-0334.roi' drawn with label 196.\n", "ROI '0633-0365.roi' drawn with label 197.\n", "ROI '0629-0418.roi' drawn with label 198.\n", "ROI '0595-0564.roi' drawn with label 199.\n", "ROI '0530-0288.roi' drawn with label 200.\n", "ROI '0535-0266.roi' drawn with label 201.\n", "ROI '0548-0229.roi' drawn with label 202.\n", "ROI '0549-0178.roi' drawn with label 203.\n", "ROI '0570-0251.roi' drawn with label 204.\n", "ROI '0610-0281.roi' drawn with label 205.\n", "ROI '0528-0096.roi' drawn with label 206.\n", "ROI '0541-0084.roi' drawn with label 207.\n", "ROI '0523-0074.roi' drawn with label 208.\n", "ROI '0529-0045.roi' drawn with label 209.\n", "ROI '0566-0014.roi' drawn with label 210.\n", "ROI '0584-0037.roi' drawn with label 211.\n", "ROI '0594-0046.roi' drawn with label 212.\n", "ROI '0581-0057.roi' drawn with label 213.\n", "ROI '0588-0083.roi' drawn with label 214.\n", "ROI '0662-0094.roi' drawn with label 215.\n", "ROI '0664-0064.roi' drawn with label 216.\n", "ROI '0664-0045.roi' drawn with label 217.\n", "ROI '0682-0019.roi' drawn with label 218.\n", "ROI '0617-0156.roi' drawn with label 219.\n", "ROI '0677-0132.roi' drawn with label 220.\n", "ROI '0709-0126.roi' drawn with label 221.\n", "ROI '0731-0116.roi' drawn with label 222.\n", "ROI '0749-0113.roi' drawn with label 223.\n", "ROI '0768-0109.roi' drawn with label 224.\n", "ROI '0730-0054.roi' drawn with label 225.\n", "ROI '0736-0042.roi' drawn with label 226.\n", "ROI '0744-0033.roi' drawn with label 227.\n", "ROI '0763-0041.roi' drawn with label 228.\n", "ROI '0801-0066.roi' drawn with label 229.\n", "ROI '0811-0074.roi' drawn with label 230.\n", "ROI '0826-0054.roi' drawn with label 231.\n", "ROI '0895-0017.roi' drawn with label 232.\n", "ROI '0901-0028.roi' drawn with label 233.\n", "ROI '0892-0053.roi' drawn with label 234.\n", "ROI '0912-0056.roi' drawn with label 235.\n", "ROI '0869-0078.roi' drawn with label 236.\n", "ROI '0857-0091.roi' drawn with label 237.\n", "ROI '0872-0095.roi' drawn with label 238.\n", "ROI '0789-0167.roi' drawn with label 239.\n", "ROI '0851-0204.roi' drawn with label 240.\n", "ROI '0809-0187.roi' drawn with label 241.\n", "ROI '0794-0197.roi' drawn with label 242.\n", "ROI '0778-0204.roi' drawn with label 243.\n", "ROI '0760-0188.roi' drawn with label 244.\n", "ROI '0757-0202.roi' drawn with label 245.\n", "ROI '0693-0196.roi' drawn with label 246.\n", "ROI '0642-0175.roi' drawn with label 247.\n", "ROI '0632-0184.roi' drawn with label 248.\n", "ROI '0666-0181.roi' drawn with label 249.\n", "ROI '0659-0299.roi' drawn with label 250.\n", "ROI '0689-0283.roi' drawn with label 251.\n", "ROI '0757-0252.roi' drawn with label 252.\n", "ROI '0772-0244.roi' drawn with label 253.\n", "ROI '0793-0248.roi' drawn with label 254.\n", "ROI '0806-0259.roi' drawn with label 255.\n", "ROI '0822-0302.roi' drawn with label 256.\n", "ROI '0842-0290.roi' drawn with label 257.\n", "ROI '0873-0310.roi' drawn with label 258.\n", "ROI '0904-0310.roi' drawn with label 259.\n", "ROI '0766-0366.roi' drawn with label 260.\n", "ROI '0672-0439.roi' drawn with label 261.\n", "ROI '0662-0463.roi' drawn with label 262.\n", "ROI '1025-0049.roi' drawn with label 263.\n", "ROI '1029-0012.roi' drawn with label 264.\n", "ROI '1023-0127.roi' drawn with label 265.\n", "ROI '1027-0160.roi' drawn with label 266.\n", "ROI '0972-0183.roi' drawn with label 267.\n", "ROI '0950-0237.roi' drawn with label 268.\n", "ROI '0987-0238.roi' drawn with label 269.\n", "ROI '0953-0284.roi' drawn with label 270.\n", "ROI '0971-0267.roi' drawn with label 271.\n", "ROI '0967-0303.roi' drawn with label 272.\n", "ROI '0989-0306.roi' drawn with label 273.\n", "ROI '1023-0271.roi' drawn with label 274.\n", "ROI '1030-0259.roi' drawn with label 275.\n", "ROI '1033-0288.roi' drawn with label 276.\n", "ROI '1036-0324.roi' drawn with label 277.\n", "ROI '0980-0355.roi' drawn with label 278.\n", "ROI '0942-0349.roi' drawn with label 279.\n", "ROI '0900-0364.roi' drawn with label 280.\n", "ROI '0919-0377.roi' drawn with label 281.\n", "ROI '0914-0393.roi' drawn with label 282.\n", "ROI '0890-0412.roi' drawn with label 283.\n", "ROI '0846-0420.roi' drawn with label 284.\n", "ROI '0852-0433.roi' drawn with label 285.\n", "ROI '1016-0479.roi' drawn with label 286.\n", "ROI '0990-0481.roi' drawn with label 287.\n", "ROI '0999-0527.roi' drawn with label 288.\n", "ROI '1001-0545.roi' drawn with label 289.\n", "ROI '0983-0530.roi' drawn with label 290.\n", "ROI '0916-0518.roi' drawn with label 291.\n", "ROI '0903-0528.roi' drawn with label 292.\n", "ROI '0897-0501.roi' drawn with label 293.\n", "ROI '0828-0463.roi' drawn with label 294.\n", "ROI '0827-0437.roi' drawn with label 295.\n", "ROI '0805-0421.roi' drawn with label 296.\n", "ROI '0781-0411.roi' drawn with label 297.\n", "ROI '0786-0385.roi' drawn with label 298.\n", "ROI '0772-0427.roi' drawn with label 299.\n", "ROI '0747-0491.roi' drawn with label 300.\n", "ROI '0696-0495.roi' drawn with label 301.\n", "ROI '0687-0510.roi' drawn with label 302.\n", "ROI '0660-0504.roi' drawn with label 303.\n", "ROI '0631-0546.roi' drawn with label 304.\n", "ROI '0628-0572.roi' drawn with label 305.\n", "ROI '0662-0547.roi' drawn with label 306.\n", "ROI '0683-0535.roi' drawn with label 307.\n", "ROI '0694-0556.roi' drawn with label 308.\n", "ROI '0818-0490.roi' drawn with label 309.\n", "ROI '0860-0404.roi' drawn with label 310.\n", "ROI '0813-0531.roi' drawn with label 311.\n", "ROI '0834-0529.roi' drawn with label 312.\n", "ROI '0813-0577.roi' drawn with label 313.\n", "ROI '0783-0598.roi' drawn with label 314.\n", "ROI '0792-0617.roi' drawn with label 315.\n", "ROI '0770-0631.roi' drawn with label 316.\n", "ROI '0744-0642.roi' drawn with label 317.\n", "ROI '0719-0591.roi' drawn with label 318.\n", "ROI '0699-0585.roi' drawn with label 319.\n", "ROI '0835-0574.roi' drawn with label 320.\n", "ROI '0862-0545.roi' drawn with label 321.\n", "ROI '0864-0562.roi' drawn with label 322.\n", "ROI '0893-0558.roi' drawn with label 323.\n", "ROI '0941-0422.roi' drawn with label 324.\n", "ROI '0983-0570.roi' drawn with label 325.\n", "ROI '1001-0586.roi' drawn with label 326.\n", "ROI '1002-0607.roi' drawn with label 327.\n", "ROI '0980-0614.roi' drawn with label 328.\n", "ROI '0881-0625.roi' drawn with label 329.\n", "ROI '1028-0643.roi' drawn with label 330.\n", "ROI '0999-0660.roi' drawn with label 331.\n", "ROI '0982-0681.roi' drawn with label 332.\n", "ROI '0956-0708.roi' drawn with label 333.\n", "ROI '0928-0708.roi' drawn with label 334.\n", "ROI '0909-0691.roi' drawn with label 335.\n", "ROI '0890-0668.roi' drawn with label 336.\n", "ROI '0876-0684.roi' drawn with label 337.\n", "ROI '0856-0704.roi' drawn with label 338.\n", "ROI '0838-0704.roi' drawn with label 339.\n", "ROI '0801-0702.roi' drawn with label 340.\n", "ROI '0840-0681.roi' drawn with label 341.\n", "ROI '0818-0673.roi' drawn with label 342.\n", "ROI '0819-0655.roi' drawn with label 343.\n", "ROI '0932-0767.roi' drawn with label 344.\n", "ROI '0953-0774.roi' drawn with label 345.\n", "ROI '0983-0775.roi' drawn with label 346.\n", "ROI '1035-0752.roi' drawn with label 347.\n", "ROI '1008-0834.roi' drawn with label 348.\n", "ROI '1023-0847.roi' drawn with label 349.\n", "ROI '1011-0859.roi' drawn with label 350.\n", "ROI '0993-0852.roi' drawn with label 351.\n", "ROI '1011-0887.roi' drawn with label 352.\n", "ROI '0957-0910.roi' drawn with label 353.\n", "ROI '0916-0890.roi' drawn with label 354.\n", "ROI '0831-0886.roi' drawn with label 355.\n", "ROI '0863-0782.roi' drawn with label 356.\n", "ROI '0662-0611.roi' drawn with label 357.\n", "ROI '0658-0630.roi' drawn with label 358.\n", "ROI '0620-0651.roi' drawn with label 359.\n", "ROI '0597-0679.roi' drawn with label 360.\n", "ROI '0590-0722.roi' drawn with label 361.\n", "ROI '0606-0718.roi' drawn with label 362.\n", "ROI '0680-0642.roi' drawn with label 363.\n", "ROI '0697-0661.roi' drawn with label 364.\n", "ROI '0714-0686.roi' drawn with label 365.\n", "ROI '0691-0708.roi' drawn with label 366.\n", "ROI '0731-0738.roi' drawn with label 367.\n", "ROI '0757-0741.roi' drawn with label 368.\n", "ROI '0759-0703.roi' drawn with label 369.\n", "ROI '0790-0736.roi' drawn with label 370.\n", "ROI '0798-0751.roi' drawn with label 371.\n", "ROI '0781-0765.roi' drawn with label 372.\n", "ROI '0775-0783.roi' drawn with label 373.\n", "ROI '0731-0784.roi' drawn with label 374.\n", "ROI '0730-0801.roi' drawn with label 375.\n", "ROI '0706-0793.roi' drawn with label 376.\n", "ROI '0694-0813.roi' drawn with label 377.\n", "ROI '0676-0805.roi' drawn with label 378.\n", "ROI '0617-0789.roi' drawn with label 379.\n", "ROI '0606-0803.roi' drawn with label 380.\n", "ROI '0539-0756.roi' drawn with label 381.\n", "ROI '0530-0725.roi' drawn with label 382.\n", "ROI '0495-0699.roi' drawn with label 383.\n", "ROI '0459-0705.roi' drawn with label 384.\n", "ROI '0458-0732.roi' drawn with label 385.\n", "ROI '0440-0733.roi' drawn with label 386.\n", "ROI '0425-0734.roi' drawn with label 387.\n", "ROI '0400-0723.roi' drawn with label 388.\n", "ROI '0387-0724.roi' drawn with label 389.\n", "ROI '0361-0700.roi' drawn with label 390.\n", "ROI '0367-0772.roi' drawn with label 391.\n", "ROI '0399-0805.roi' drawn with label 392.\n", "ROI '0388-0823.roi' drawn with label 393.\n", "ROI '0462-0816.roi' drawn with label 394.\n", "ROI '0456-0830.roi' drawn with label 395.\n", "ROI '0489-0848.roi' drawn with label 396.\n", "ROI '0493-0825.roi' drawn with label 397.\n", "ROI '0491-0869.roi' drawn with label 398.\n", "ROI '0458-0903.roi' drawn with label 399.\n", "ROI '0469-0893.roi' drawn with label 400.\n", "ROI '0443-0881.roi' drawn with label 401.\n", "ROI '0411-0882.roi' drawn with label 402.\n", "ROI '0407-0899.roi' drawn with label 403.\n", "ROI '0379-0885.roi' drawn with label 404.\n", "ROI '0371-0905.roi' drawn with label 405.\n", "ROI '0356-0912.roi' drawn with label 406.\n", "ROI '0495-0906.roi' drawn with label 407.\n", "ROI '0530-0879.roi' drawn with label 408.\n", "ROI '0554-0860.roi' drawn with label 409.\n", "ROI '0560-0841.roi' drawn with label 410.\n", "ROI '0576-0820.roi' drawn with label 411.\n", "ROI '0734-0906.roi' drawn with label 412.\n", "ROI '0764-0893.roi' drawn with label 413.\n", "ROI '0789-0848.roi' drawn with label 414.\n", "ROI '0995-0942.roi' drawn with label 415.\n", "ROI '0931-0953.roi' drawn with label 416.\n", "ROI '0936-0928.roi' drawn with label 417.\n", "ROI '0870-0955.roi' drawn with label 418.\n", "ROI '0859-0964.roi' drawn with label 419.\n", "ROI '0887-0965.roi' drawn with label 420.\n", "ROI '0890-0992.roi' drawn with label 421.\n", "ROI '0947-1012.roi' drawn with label 422.\n", "ROI '0938-1042.roi' drawn with label 423.\n", "ROI '0912-1042.roi' drawn with label 424.\n", "ROI '0931-1058.roi' drawn with label 425.\n", "ROI '0921-1075.roi' drawn with label 426.\n", "ROI '0928-1109.roi' drawn with label 427.\n", "ROI '1023-1123.roi' drawn with label 428.\n", "ROI '1034-1106.roi' drawn with label 429.\n", "ROI '0981-1167.roi' drawn with label 430.\n", "ROI '0978-1187.roi' drawn with label 431.\n", "ROI '0997-1177.roi' drawn with label 432.\n", "ROI '1002-1194.roi' drawn with label 433.\n", "ROI '1013-1185.roi' drawn with label 434.\n", "ROI '1006-1240.roi' drawn with label 435.\n", "ROI '0972-1334.roi' drawn with label 436.\n", "ROI '0937-1323.roi' drawn with label 437.\n", "ROI '0860-1320.roi' drawn with label 438.\n", "ROI '0899-1278.roi' drawn with label 439.\n", "ROI '0906-1259.roi' drawn with label 440.\n", "ROI '0921-1254.roi' drawn with label 441.\n", "ROI '0936-1254.roi' drawn with label 442.\n", "ROI '0897-1212.roi' drawn with label 443.\n", "ROI '0903-1231.roi' drawn with label 444.\n", "ROI '0887-1236.roi' drawn with label 445.\n", "ROI '0883-1222.roi' drawn with label 446.\n", "ROI '0867-1269.roi' drawn with label 447.\n", "ROI '0838-1255.roi' drawn with label 448.\n", "ROI '0809-1254.roi' drawn with label 449.\n", "ROI '0836-1216.roi' drawn with label 450.\n", "ROI '0815-1205.roi' drawn with label 451.\n", "ROI '0795-1185.roi' drawn with label 452.\n", "ROI '0792-1196.roi' drawn with label 453.\n", "ROI '0777-1206.roi' drawn with label 454.\n", "ROI '0771-1196.roi' drawn with label 455.\n", "ROI '0824-0996.roi' drawn with label 456.\n", "ROI '0785-0962.roi' drawn with label 457.\n", "ROI '0730-1029.roi' drawn with label 458.\n", "ROI '0710-0996.roi' drawn with label 459.\n", "ROI '0537-0952.roi' drawn with label 460.\n", "ROI '0502-0968.roi' drawn with label 461.\n", "ROI '0442-0972.roi' drawn with label 462.\n", "ROI '0396-0970.roi' drawn with label 463.\n", "ROI '0383-0940.roi' drawn with label 464.\n", "ROI '0388-1009.roi' drawn with label 465.\n", "ROI '0435-1011.roi' drawn with label 466.\n", "ROI '0461-0997.roi' drawn with label 467.\n", "ROI '0447-1027.roi' drawn with label 468.\n", "ROI '0512-0992.roi' drawn with label 469.\n", "ROI '0521-1010.roi' drawn with label 470.\n", "ROI '0558-1026.roi' drawn with label 471.\n", "ROI '0589-0994.roi' drawn with label 472.\n", "ROI '0606-0972.roi' drawn with label 473.\n", "ROI '0607-0947.roi' drawn with label 474.\n", "ROI '0626-0938.roi' drawn with label 475.\n", "ROI '0653-0949.roi' drawn with label 476.\n", "ROI '0657-0970.roi' drawn with label 477.\n", "ROI '0642-0990.roi' drawn with label 478.\n", "ROI '0610-1044.roi' drawn with label 479.\n", "ROI '0574-1074.roi' drawn with label 480.\n", "ROI '0684-1041.roi' drawn with label 481.\n", "ROI '0697-1033.roi' drawn with label 482.\n", "ROI '0704-1059.roi' drawn with label 483.\n", "ROI '0720-1069.roi' drawn with label 484.\n", "ROI '0741-1075.roi' drawn with label 485.\n", "ROI '0730-1142.roi' drawn with label 486.\n", "ROI '0689-1172.roi' drawn with label 487.\n", "ROI '0696-1216.roi' drawn with label 488.\n", "ROI '0658-1217.roi' drawn with label 489.\n", "ROI '0663-1197.roi' drawn with label 490.\n", "ROI '0650-1186.roi' drawn with label 491.\n", "ROI '0639-1201.roi' drawn with label 492.\n", "ROI '0625-1169.roi' drawn with label 493.\n", "ROI '0591-1161.roi' drawn with label 494.\n", "ROI '0603-1200.roi' drawn with label 495.\n", "ROI '0577-1209.roi' drawn with label 496.\n", "ROI '0533-1112.roi' drawn with label 497.\n", "ROI '0539-1131.roi' drawn with label 498.\n", "ROI '0518-1127.roi' drawn with label 499.\n", "ROI '0485-1160.roi' drawn with label 500.\n", "ROI '0465-1147.roi' drawn with label 501.\n", "ROI '0440-1212.roi' drawn with label 502.\n", "ROI '0445-1236.roi' drawn with label 503.\n", "ROI '0422-1142.roi' drawn with label 504.\n", "ROI '0381-1153.roi' drawn with label 505.\n", "ROI '0386-1048.roi' drawn with label 506.\n", "ROI '0690-1306.roi' drawn with label 507.\n", "ROI '0576-1289.roi' drawn with label 508.\n", "ROI '0543-1270.roi' drawn with label 509.\n", "ROI '0506-1233.roi' drawn with label 510.\n", "ROI '0481-1251.roi' drawn with label 511.\n", "ROI '0513-1297.roi' drawn with label 512.\n", "ROI '0490-1306.roi' drawn with label 513.\n", "ROI '0523-1338.roi' drawn with label 514.\n", "ROI '0422-1287.roi' drawn with label 515.\n", "ROI '0404-1282.roi' drawn with label 516.\n", "ROI '0377-1270.roi' drawn with label 517.\n", "ROI '0792-1351.roi' drawn with label 518.\n", "ROI '0674-1377.roi' drawn with label 519.\n", "ROI '0504-1375.roi' drawn with label 520.\n", "ROI '0413-1380.roi' drawn with label 521.\n", "ROI '0430-1356.roi' drawn with label 522.\n", "ROI '0372-1363.roi' drawn with label 523.\n", "ROI '0348-1352.roi' drawn with label 524.\n", "ROI '0297-1330.roi' drawn with label 525.\n", "ROI '0312-1301.roi' drawn with label 526.\n", "ROI '0311-1270.roi' drawn with label 527.\n", "ROI '0337-1248.roi' drawn with label 528.\n", "ROI '0311-1213.roi' drawn with label 529.\n", "ROI '0295-1199.roi' drawn with label 530.\n", "ROI '0305-1188.roi' drawn with label 531.\n", "ROI '0321-1159.roi' drawn with label 532.\n", "ROI '0304-1162.roi' drawn with label 533.\n", "ROI '0285-1146.roi' drawn with label 534.\n", "ROI '0234-1099.roi' drawn with label 535.\n", "ROI '0350-1037.roi' drawn with label 536.\n", "ROI '0351-1065.roi' drawn with label 537.\n", "ROI '0318-1066.roi' drawn with label 538.\n", "ROI '0325-1053.roi' drawn with label 539.\n", "ROI '0323-1031.roi' drawn with label 540.\n", "ROI '0279-1053.roi' drawn with label 541.\n", "ROI '0294-1035.roi' drawn with label 542.\n", "ROI '0267-1029.roi' drawn with label 543.\n", "ROI '0289-1006.roi' drawn with label 544.\n", "ROI '0326-0969.roi' drawn with label 545.\n", "ROI '0362-0979.roi' drawn with label 546.\n", "ROI '0346-0987.roi' drawn with label 547.\n", "ROI '0346-0951.roi' drawn with label 548.\n", "ROI '0302-0921.roi' drawn with label 549.\n", "ROI '0350-0875.roi' drawn with label 550.\n", "ROI '0325-0848.roi' drawn with label 551.\n", "ROI '0347-0781.roi' drawn with label 552.\n", "ROI '0324-0784.roi' drawn with label 553.\n", "ROI '0339-0758.roi' drawn with label 554.\n", "ROI '0318-0752.roi' drawn with label 555.\n", "ROI '0319-0739.roi' drawn with label 556.\n", "ROI '0337-0716.roi' drawn with label 557.\n", "ROI '0346-0693.roi' drawn with label 558.\n", "ROI '0291-0729.roi' drawn with label 559.\n", "ROI '0277-0727.roi' drawn with label 560.\n", "ROI '0261-0749.roi' drawn with label 561.\n", "ROI '0247-0715.roi' drawn with label 562.\n", "ROI '0223-0756.roi' drawn with label 563.\n", "ROI '0203-0769.roi' drawn with label 564.\n", "ROI '0187-0726.roi' drawn with label 565.\n", "ROI '0148-0720.roi' drawn with label 566.\n", "ROI '0134-0723.roi' drawn with label 567.\n", "ROI '0120-0701.roi' drawn with label 568.\n", "ROI '0158-0762.roi' drawn with label 569.\n", "ROI '0128-0789.roi' drawn with label 570.\n", "ROI '0128-0772.roi' drawn with label 571.\n", "ROI '0108-0757.roi' drawn with label 572.\n", "ROI '0088-0750.roi' drawn with label 573.\n", "ROI '0075-0787.roi' drawn with label 574.\n", "ROI '0077-0824.roi' drawn with label 575.\n", "ROI '0046-0766.roi' drawn with label 576.\n", "ROI '0048-0735.roi' drawn with label 577.\n", "ROI '0019-0723.roi' drawn with label 578.\n", "ROI '0035-0715.roi' drawn with label 579.\n", "ROI '0227-0814.roi' drawn with label 580.\n", "ROI '0268-0810.roi' drawn with label 581.\n", "ROI '0236-0832.roi' drawn with label 582.\n", "ROI '0269-0891.roi' drawn with label 583.\n", "ROI '0283-0904.roi' drawn with label 584.\n", "ROI '0283-0887.roi' drawn with label 585.\n", "ROI '0299-0883.roi' drawn with label 586.\n", "ROI '0237-0923.roi' drawn with label 587.\n", "ROI '0222-0945.roi' drawn with label 588.\n", "ROI '0175-0926.roi' drawn with label 589.\n", "ROI '0147-0919.roi' drawn with label 590.\n", "ROI '0134-0930.roi' drawn with label 591.\n", "ROI '0108-0917.roi' drawn with label 592.\n", "ROI '0118-0909.roi' drawn with label 593.\n", "ROI '0090-0902.roi' drawn with label 594.\n", "ROI '0069-0896.roi' drawn with label 595.\n", "ROI '0075-0881.roi' drawn with label 596.\n", "ROI '0092-0882.roi' drawn with label 597.\n", "ROI '0105-0859.roi' drawn with label 598.\n", "ROI '0125-0864.roi' drawn with label 599.\n", "ROI '0019-0840.roi' drawn with label 600.\n", "ROI '0015-0929.roi' drawn with label 601.\n", "ROI '0011-0958.roi' drawn with label 602.\n", "ROI '0028-0983.roi' drawn with label 603.\n", "ROI '0040-0970.roi' drawn with label 604.\n", "ROI '0044-0999.roi' drawn with label 605.\n", "ROI '0066-0991.roi' drawn with label 606.\n", "ROI '0083-0949.roi' drawn with label 607.\n", "ROI '0105-0983.roi' drawn with label 608.\n", "ROI '0123-0966.roi' drawn with label 609.\n", "ROI '0043-1045.roi' drawn with label 610.\n", "ROI '0009-1072.roi' drawn with label 611.\n", "ROI '0053-1060.roi' drawn with label 612.\n", "ROI '0085-1086.roi' drawn with label 613.\n", "ROI '0148-1039.roi' drawn with label 614.\n", "ROI '0203-1024.roi' drawn with label 615.\n", "ROI '0190-1054.roi' drawn with label 616.\n", "ROI '0192-1071.roi' drawn with label 617.\n", "ROI '0195-1047.roi' drawn with label 618.\n", "ROI '0211-1041.roi' drawn with label 619.\n", "ROI '0192-1107.roi' drawn with label 620.\n", "ROI '0162-1113.roi' drawn with label 621.\n", "ROI '0117-1106.roi' drawn with label 622.\n", "ROI '0112-1127.roi' drawn with label 623.\n", "ROI '0031-1144.roi' drawn with label 624.\n", "ROI '0016-1161.roi' drawn with label 625.\n", "ROI '0038-1182.roi' drawn with label 626.\n", "ROI '0006-1209.roi' drawn with label 627.\n", "ROI '0019-1242.roi' drawn with label 628.\n", "ROI '0011-1263.roi' drawn with label 629.\n", "ROI '0050-1256.roi' drawn with label 630.\n", "ROI '0094-1241.roi' drawn with label 631.\n", "ROI '0103-1214.roi' drawn with label 632.\n", "ROI '0091-1170.roi' drawn with label 633.\n", "ROI '0108-1175.roi' drawn with label 634.\n", "ROI '0121-1182.roi' drawn with label 635.\n", "ROI '0142-1140.roi' drawn with label 636.\n", "ROI '0159-1164.roi' drawn with label 637.\n", "ROI '0180-1150.roi' drawn with label 638.\n", "ROI '0205-1153.roi' drawn with label 639.\n", "ROI '0222-1181.roi' drawn with label 640.\n", "ROI '0148-1244.roi' drawn with label 641.\n", "ROI '0079-1304.roi' drawn with label 642.\n", "ROI '0026-1301.roi' drawn with label 643.\n", "ROI '0024-1375.roi' drawn with label 644.\n", "ROI '0077-1369.roi' drawn with label 645.\n", "ROI '0075-1349.roi' drawn with label 646.\n", "ROI '0095-1355.roi' drawn with label 647.\n", "ROI '0108-1376.roi' drawn with label 648.\n", "ROI '0102-1335.roi' drawn with label 649.\n", "ROI '0123-1351.roi' drawn with label 650.\n", "ROI '0208-1373.roi' drawn with label 651.\n", "ROI '0226-1379.roi' drawn with label 652.\n", "ROI '0233-1367.roi' drawn with label 653.\n", "ROI '0255-1382.roi' drawn with label 654.\n", "ROI '0276-1259.roi' drawn with label 655.\n", "ROI '0338-0207.roi' drawn with label 656.\n", "ROI '0666-0423.roi' drawn with label 657.\n", "ROI '0976-0419.roi' drawn with label 658.\n", "ROI '0983-0417.roi' drawn with label 659.\n", "ROI '0957-0257.roi' drawn with label 660.\n", "ROI '0730-0694.roi' drawn with label 661.\n", "ROI '0725-0703.roi' drawn with label 662.\n", "ROI '0741-0702.roi' drawn with label 663.\n", "ROI '0742-0717.roi' drawn with label 664.\n", "ROI '0709-0407.roi' drawn with label 665.\n", "ROI '0708-0393.roi' drawn with label 666.\n", "ROI '0697-0401.roi' drawn with label 667.\n", "ROI '0473-0845.roi' drawn with label 668.\n", "ROI '0956-1029.roi' drawn with label 669.\n", "ROI '0759-1331.roi' drawn with label 670.\n", "ROI '0744-1326.roi' drawn with label 671.\n", "ROI '0731-1333.roi' drawn with label 672.\n", "ROI '0733-1370.roi' drawn with label 673.\n", "ROI '0727-1380.roi' drawn with label 674.\n", "ROI '0644-1372.roi' drawn with label 675.\n", "ROI '0062-1008.roi' drawn with label 676.\n", "ROI '0204-1328.roi' drawn with label 677.\n", "ROI '0169-1299.roi' drawn with label 678.\n", "ROI '0187-1279.roi' drawn with label 679.\n", "ROI '0198-1273.roi' drawn with label 680.\n", "ROI '0131-1188.roi' drawn with label 681.\n", "ROI '0170-1175.roi' drawn with label 682.\n", "ROI '0184-1183.roi' drawn with label 683.\n", "ROI '0026-1230.roi' drawn with label 684.\n", "ROI '0852-0202.roi' drawn with label 1.\n", "ROI '0821-0530.roi' drawn with label 2.\n", "ROI '0157-0717.roi' drawn with label 3.\n", "ROI '0309-0918.roi' drawn with label 4.\n", "ROI '0723-0801.roi' drawn with label 5.\n", "ROI '0856-0699.roi' drawn with label 6.\n", "ROI '0936-0918.roi' drawn with label 7.\n", "ROI '0379-1147.roi' drawn with label 8.\n", "ROI '0723-0696.roi' drawn with label 9.\n", "ROI '0827-0580.roi' drawn with label 10.\n", "ROI '1032-0745.roi' drawn with label 11.\n", "ROI '0824-0992.roi' drawn with label 12.\n", "ROI '0051-0629.roi' drawn with label 13.\n", "ROI '0382-0539.roi' drawn with label 14.\n", "ROI '0438-0455.roi' drawn with label 15.\n", "ROI '0591-0506.roi' drawn with label 16.\n", "ROI '0659-0541.roi' drawn with label 17.\n", "ROI '0598-0674.roi' drawn with label 18.\n", "ROI '0379-0875.roi' drawn with label 19.\n", "ROI '0995-0940.roi' drawn with label 20.\n", "ROI '1007-1236.roi' drawn with label 21.\n", "ROI '0864-1264.roi' drawn with label 22.\n", "ROI '1007-0479.roi' drawn with label 23.\n", "ROI '0209-1152.roi' drawn with label 24.\n", "ROI '0405-0654.roi' drawn with label 25.\n", "ROI '0030-1143.roi' drawn with label 26.\n", "ROI '0017-1167.roi' drawn with label 27.\n", "ROI '0550-0841.roi' drawn with label 28.\n", "ROI '0927-1070.roi' drawn with label 29.\n", "ROI '0573-1286.roi' drawn with label 30.\n", "ROI '0444-1232.roi' drawn with label 31.\n", "ROI '0344-1343.roi' drawn with label 32.\n"]}], "source": ["# Convert ROI zip files to masks and save as numpy arrays\n", "dapi_mask = rois_to_mask(\"/Users/<USER>/Downloads/ddd/GFAP_RoiSet_ALLDAPI_Final.zip\", 1388, 1040)\n", "np.save(\"gfap_4201_dapimultimask.npy\", dapi_mask)\n", "\n", "cellbodies_mask = rois_to_mask(\"/Users/<USER>/Downloads/ddd/GFAP_RoiSet_CellBodies_Final.zip\", 1388, 1040) \n", "np.save(\"gfap_4201_cellbodiesmultimask.npy\", cellbodies_mask)"]}, {"cell_type": "code", "execution_count": null, "id": "1db2fe9d", "metadata": {}, "outputs": [], "source": ["dapitobeseg="]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 5}