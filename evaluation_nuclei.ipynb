{"cells": [{"cell_type": "code", "execution_count": 7, "id": "2b9e5636", "metadata": {}, "outputs": [], "source": ["\n", "import importlib\n", "import ifimage_tools\n", "importlib.reload(ifimage_tools)\n", "import pandas as pd\n", "import warnings\n", "import pickle\n", "\n", "import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "image_dir = \"/home/<USER>/ifimage/Reorgnized Ground Truth\"\n", "dataset = ifimage_tools.IfImageDataset(image_dir, \"/home/<USER>/ifimage/merged_mask\", {})\n", "dataset.load_data()\n", "old_version_sample_ids = [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]\n", "for sample_id in old_version_sample_ids:\n", "    if sample_id in dataset.samples:\n", "        del dataset.samples[sample_id]"]}, {"cell_type": "code", "execution_count": null, "id": "b44547d3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["nuclei eval:   8%|▊         | 8/100 [00:30<05:30,  3.59s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [15972/cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto2/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cyto3/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellposeSAM/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/StarDist2D/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["nuclei eval:  39%|███▉      | 39/100 [02:18<03:38,  3.58s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Sample 8550 lacks GT nuclei; skipping…\n", "Sample 8917 lacks GT nuclei; skipping…\n", "Sample 5191 lacks GT nuclei; skipping…\n", "Sample 6833 lacks GT nuclei; skipping…\n", "Sample 10166 lacks GT nuclei; skipping…\n", "Sample 9106 lacks GT nuclei; skipping…\n", "Sample 4642 lacks GT nuclei; skipping…\n", "Sample 5792 lacks GT nuclei; skipping…\n", "Sample 10061 lacks GT nuclei; skipping…\n", "Sample 1110 lacks GT nuclei; skipping…\n", "Sample 7925 lacks GT nuclei; skipping…\n", "Sample 5863 lacks GT nuclei; skipping…\n", "Sample 1112 lacks GT nuclei; skipping…\n", "Sample 7739 lacks GT nuclei; skipping…\n", "Sample 5059 lacks GT nuclei; skipping…\n", "Sample 7113 lacks GT nuclei; skipping…\n", "Sample 3569 lacks GT nuclei; skipping…\n", "Sample 7685 lacks GT nuclei; skipping…\n", "Sample 8224 lacks GT nuclei; skipping…\n", "Sample 4515 lacks GT nuclei; skipping…\n", "Sample 7870 lacks GT nuclei; skipping…\n", "Sample 6790 lacks GT nuclei; skipping…\n", "Sample 7962 lacks GT nuclei; skipping…\n", "Sample 1111 lacks GT nuclei; skipping…\n", "Sample 8746 lacks GT nuclei; skipping…\n", "Sample 3532 lacks GT nuclei; skipping…\n", "Sample 3999 lacks GT nuclei; skipping…\n", "Sample 3527 lacks GT nuclei; skipping…\n", "Sample 7071 lacks GT nuclei; skipping…\n", "Sample 5923 lacks GT nuclei; skipping…\n", "Sample 5789 lacks GT nuclei; skipping…\n", "Sample 5794 lacks GT nuclei; skipping…\n", "Sample 9755 lacks GT nuclei; skipping…\n", "Sample 6020 lacks GT nuclei; skipping…\n", "Sample 9170 lacks GT nuclei; skipping…\n", "Sample 10015 lacks GT nuclei; skipping…\n", "Sample 1116 lacks GT nuclei; skipping…\n", "Sample 4238 lacks GT nuclei; skipping…\n", "Sample 1114 lacks GT nuclei; skipping…\n", "Sample 5795 lacks GT nuclei; skipping…\n", "Sample 4548 lacks GT nuclei; skipping…\n", "Sample 8517 lacks GT nuclei; skipping…\n", "Sample 9783 lacks GT nuclei; skipping…\n", "Sample 1108 lacks GT nuclei; skipping…\n", "Sample 8310 lacks GT nuclei; skipping…\n", "Sample 9472 lacks GT nuclei; skipping…\n", "Sample 4736 lacks GT nuclei; skipping…\n", "Sample 7319 lacks GT nuclei; skipping…\n", "Sample 9866 lacks GT nuclei; skipping…\n", "Sample 6212 lacks GT nuclei; skipping…\n", "Sample 8942 lacks GT nuclei; skipping…\n", "Sample 1113 lacks GT nuclei; skipping…\n", "Sample 6748 lacks GT nuclei; skipping…\n", "Sample 1115 lacks GT nuclei; skipping…\n", "Sample 5410 lacks GT nuclei; skipping…\n"]}, {"name": "stderr", "output_type": "stream", "text": ["nuclei eval: 100%|██████████| 100/100 [02:22<00:00,  1.43s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Sample 6523 lacks GT nuclei; skipping…\n", "Sample 6466 lacks GT nuclei; skipping…\n", "Sample 4683 lacks GT nuclei; skipping…\n", "Sample 4319 lacks GT nuclei; skipping…\n", "Sample 1109 lacks GT nuclei; skipping…\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval: 100%|██████████| 100/100 [00:00<00:00, 39587.58it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5803] no cell mask for 'cellpose'; skip\n", "[5803] no cell mask for 'watershed'; skip\n", "[5803] no cell mask for 'cell_expansion'; skip\n", "[5803] no cell mask for 'watershed_only_cyto'; skip\n", "[5803] no cell mask for 'cellpose2chan'; skip\n", "[12781] no cell mask for 'cellpose'; skip\n", "[12781] no cell mask for 'watershed'; skip\n", "[12781] no cell mask for 'cell_expansion'; skip\n", "[12781] no cell mask for 'watershed_only_cyto'; skip\n", "[12781] no cell mask for 'cellpose2chan'; skip\n", "[12779] no cell mask for 'cellpose'; skip\n", "[12779] no cell mask for 'watershed'; skip\n", "[12779] no cell mask for 'cell_expansion'; skip\n", "[12779] no cell mask for 'watershed_only_cyto'; skip\n", "[12779] no cell mask for 'cellpose2chan'; skip\n", "[12786] no cell mask for 'cellpose'; skip\n", "[12786] no cell mask for 'watershed'; skip\n", "[12786] no cell mask for 'cell_expansion'; skip\n", "[12786] no cell mask for 'watershed_only_cyto'; skip\n", "[12786] no cell mask for 'cellpose2chan'; skip\n", "[10594] no cell mask for 'cellpose'; skip\n", "[10594] no cell mask for 'watershed'; skip\n", "[10594] no cell mask for 'cell_expansion'; skip\n", "[10594] no cell mask for 'watershed_only_cyto'; skip\n", "[10594] no cell mask for 'cellpose2chan'; skip\n", "[8408jan22] no cell mask for 'cellpose'; skip\n", "[8408jan22] no cell mask for 'watershed'; skip\n", "[8408jan22] no cell mask for 'cell_expansion'; skip\n", "[8408jan22] no cell mask for 'watershed_only_cyto'; skip\n", "[8408jan22] no cell mask for 'cellpose2chan'; skip\n", "[15591] no cell mask for 'cellpose'; skip\n", "[15591] no cell mask for 'watershed'; skip\n", "[15591] no cell mask for 'cell_expansion'; skip\n", "[15591] no cell mask for 'watershed_only_cyto'; skip\n", "[15591] no cell mask for 'cellpose2chan'; skip\n", "[12793] no cell mask for 'cellpose'; skip\n", "[12793] no cell mask for 'watershed'; skip\n", "[12793] no cell mask for 'cell_expansion'; skip\n", "[12793] no cell mask for 'watershed_only_cyto'; skip\n", "[12793] no cell mask for 'cellpose2chan'; skip\n", "[15972] no cell mask for 'cellpose'; skip\n", "[15972] no cell mask for 'watershed'; skip\n", "[15972] no cell mask for 'cell_expansion'; skip\n", "[15972] no cell mask for 'watershed_only_cyto'; skip\n", "[15972] no cell mask for 'cellpose2chan'; skip\n", "[12794] no cell mask for 'cellpose'; skip\n", "[12794] no cell mask for 'watershed'; skip\n", "[12794] no cell mask for 'cell_expansion'; skip\n", "[12794] no cell mask for 'watershed_only_cyto'; skip\n", "[12794] no cell mask for 'cellpose2chan'; skip\n", "[1120] no cell mask for 'cellpose'; skip\n", "[1120] no cell mask for 'watershed'; skip\n", "[1120] no cell mask for 'cell_expansion'; skip\n", "[1120] no cell mask for 'watershed_only_cyto'; skip\n", "[1120] no cell mask for 'cellpose2chan'; skip\n", "[12795] no cell mask for 'cellpose'; skip\n", "[12795] no cell mask for 'watershed'; skip\n", "[12795] no cell mask for 'cell_expansion'; skip\n", "[12795] no cell mask for 'watershed_only_cyto'; skip\n", "[12795] no cell mask for 'cellpose2chan'; skip\n", "[12792] no cell mask for 'cellpose'; skip\n", "[12792] no cell mask for 'watershed'; skip\n", "[12792] no cell mask for 'cell_expansion'; skip\n", "[12792] no cell mask for 'watershed_only_cyto'; skip\n", "[12792] no cell mask for 'cellpose2chan'; skip\n", "[12787] no cell mask for 'cellpose'; skip\n", "[12787] no cell mask for 'watershed'; skip\n", "[12787] no cell mask for 'cell_expansion'; skip\n", "[12787] no cell mask for 'watershed_only_cyto'; skip\n", "[12787] no cell mask for 'cellpose2chan'; skip\n", "[12780] no cell mask for 'cellpose'; skip\n", "[12780] no cell mask for 'watershed'; skip\n", "[12780] no cell mask for 'cell_expansion'; skip\n", "[12780] no cell mask for 'watershed_only_cyto'; skip\n", "[12780] no cell mask for 'cellpose2chan'; skip\n", "[2529] no cell mask for 'cellpose'; skip\n", "[2529] no cell mask for 'watershed'; skip\n", "[2529] no cell mask for 'cell_expansion'; skip\n", "[2529] no cell mask for 'watershed_only_cyto'; skip\n", "[2529] no cell mask for 'cellpose2chan'; skip\n", "[8265] no cell mask for 'cellpose'; skip\n", "[8265] no cell mask for 'watershed'; skip\n", "[8265] no cell mask for 'cell_expansion'; skip\n", "[8265] no cell mask for 'watershed_only_cyto'; skip\n", "[8265] no cell mask for 'cellpose2chan'; skip\n", "[15973] no cell mask for 'cellpose'; skip\n", "[15973] no cell mask for 'watershed'; skip\n", "[15973] no cell mask for 'cell_expansion'; skip\n", "[15973] no cell mask for 'watershed_only_cyto'; skip\n", "[15973] no cell mask for 'cellpose2chan'; skip\n", "[12798] no cell mask for 'cellpose'; skip\n", "[12798] no cell mask for 'watershed'; skip\n", "[12798] no cell mask for 'cell_expansion'; skip\n", "[12798] no cell mask for 'watershed_only_cyto'; skip\n", "[12798] no cell mask for 'cellpose2chan'; skip\n", "[8407jan22] no cell mask for 'cellpose'; skip\n", "[8407jan22] no cell mask for 'watershed'; skip\n", "[8407jan22] no cell mask for 'cell_expansion'; skip\n", "[8407jan22] no cell mask for 'watershed_only_cyto'; skip\n", "[8407jan22] no cell mask for 'cellpose2chan'; skip\n", "[8406jan22] no cell mask for 'cellpose'; skip\n", "[8406jan22] no cell mask for 'watershed'; skip\n", "[8406jan22] no cell mask for 'cell_expansion'; skip\n", "[8406jan22] no cell mask for 'watershed_only_cyto'; skip\n", "[8406jan22] no cell mask for 'cellpose2chan'; skip\n", "[12799] no cell mask for 'cellpose'; skip\n", "[12799] no cell mask for 'watershed'; skip\n", "[12799] no cell mask for 'cell_expansion'; skip\n", "[12799] no cell mask for 'watershed_only_cyto'; skip\n", "[12799] no cell mask for 'cellpose2chan'; skip\n", "[8405jan22] no cell mask for 'cellpose'; skip\n", "[8405jan22] no cell mask for 'watershed'; skip\n", "[8405jan22] no cell mask for 'cell_expansion'; skip\n", "[8405jan22] no cell mask for 'watershed_only_cyto'; skip\n", "[8405jan22] no cell mask for 'cellpose2chan'; skip\n", "[15970] no cell mask for 'cellpose'; skip\n", "[15970] no cell mask for 'watershed'; skip\n", "[15970] no cell mask for 'cell_expansion'; skip\n", "[15970] no cell mask for 'watershed_only_cyto'; skip\n", "[15970] no cell mask for 'cellpose2chan'; skip\n", "[12791] no cell mask for 'cellpose'; skip\n", "[12791] no cell mask for 'watershed'; skip\n", "[12791] no cell mask for 'cell_expansion'; skip\n", "[12791] no cell mask for 'watershed_only_cyto'; skip\n", "[12791] no cell mask for 'cellpose2chan'; skip\n", "[12784] no cell mask for 'cellpose'; skip\n", "[12784] no cell mask for 'watershed'; skip\n", "[12784] no cell mask for 'cell_expansion'; skip\n", "[12784] no cell mask for 'watershed_only_cyto'; skip\n", "[12784] no cell mask for 'cellpose2chan'; skip\n", "[15968] no cell mask for 'cellpose'; skip\n", "[15968] no cell mask for 'watershed'; skip\n", "[15968] no cell mask for 'cell_expansion'; skip\n", "[15968] no cell mask for 'watershed_only_cyto'; skip\n", "[15968] no cell mask for 'cellpose2chan'; skip\n", "[12789] no cell mask for 'cellpose'; skip\n", "[12789] no cell mask for 'watershed'; skip\n", "[12789] no cell mask for 'cell_expansion'; skip\n", "[12789] no cell mask for 'watershed_only_cyto'; skip\n", "[12789] no cell mask for 'cellpose2chan'; skip\n", "[12783] no cell mask for 'cellpose'; skip\n", "[12783] no cell mask for 'watershed'; skip\n", "[12783] no cell mask for 'cell_expansion'; skip\n", "[12783] no cell mask for 'watershed_only_cyto'; skip\n", "[12783] no cell mask for 'cellpose2chan'; skip\n", "[12796] no cell mask for 'cellpose'; skip\n", "[12796] no cell mask for 'watershed'; skip\n", "[12796] no cell mask for 'cell_expansion'; skip\n", "[12796] no cell mask for 'watershed_only_cyto'; skip\n", "[12796] no cell mask for 'cellpose2chan'; skip\n", "[15969] no cell mask for 'cellpose'; skip\n", "[15969] no cell mask for 'watershed'; skip\n", "[15969] no cell mask for 'cell_expansion'; skip\n", "[15969] no cell mask for 'watershed_only_cyto'; skip\n", "[15969] no cell mask for 'cellpose2chan'; skip\n", "[12797] no cell mask for 'cellpose'; skip\n", "[12797] no cell mask for 'watershed'; skip\n", "[12797] no cell mask for 'cell_expansion'; skip\n", "[12797] no cell mask for 'watershed_only_cyto'; skip\n", "[12797] no cell mask for 'cellpose2chan'; skip\n", "[12782] no cell mask for 'cellpose'; skip\n", "[12782] no cell mask for 'watershed'; skip\n", "[12782] no cell mask for 'cell_expansion'; skip\n", "[12782] no cell mask for 'watershed_only_cyto'; skip\n", "[12782] no cell mask for 'cellpose2chan'; skip\n", "[15971] no cell mask for 'cellpose'; skip\n", "[15971] no cell mask for 'watershed'; skip\n", "[15971] no cell mask for 'cell_expansion'; skip\n", "[15971] no cell mask for 'watershed_only_cyto'; skip\n", "[15971] no cell mask for 'cellpose2chan'; skip\n", "[12785] no cell mask for 'cellpose'; skip\n", "[12785] no cell mask for 'watershed'; skip\n", "[12785] no cell mask for 'cell_expansion'; skip\n", "[12785] no cell mask for 'watershed_only_cyto'; skip\n", "[12785] no cell mask for 'cellpose2chan'; skip\n", "[12790] no cell mask for 'cellpose'; skip\n", "[12790] no cell mask for 'watershed'; skip\n", "[12790] no cell mask for 'cell_expansion'; skip\n", "[12790] no cell mask for 'watershed_only_cyto'; skip\n", "[12790] no cell mask for 'cellpose2chan'; skip\n", "[12788] no cell mask for 'cellpose'; skip\n", "[12788] no cell mask for 'watershed'; skip\n", "[12788] no cell mask for 'cell_expansion'; skip\n", "[12788] no cell mask for 'watershed_only_cyto'; skip\n", "[12788] no cell mask for 'cellpose2chan'; skip\n", "[8409] no cell mask for 'cellpose'; skip\n", "[8409] no cell mask for 'watershed'; skip\n", "[8409] no cell mask for 'cell_expansion'; skip\n", "[8409] no cell mask for 'watershed_only_cyto'; skip\n", "[8409] no cell mask for 'cellpose2chan'; skip\n", "[6390v2] no cell mask for 'cellpose'; skip\n", "[6390v2] no cell mask for 'watershed'; skip\n", "[6390v2] no cell mask for 'cell_expansion'; skip\n", "[6390v2] no cell mask for 'watershed_only_cyto'; skip\n", "[6390v2] no cell mask for 'cellpose2chan'; skip\n", "[8550] no cell mask for 'cellpose'; skip\n", "[8550] no cell mask for 'watershed'; skip\n", "[8550] no cell mask for 'cell_expansion'; skip\n", "[8550] no cell mask for 'watershed_only_cyto'; skip\n", "[8550] no cell mask for 'cellpose2chan'; skip\n", "[8917] no cell mask for 'cellpose'; skip\n", "[8917] no cell mask for 'watershed'; skip\n", "[8917] no cell mask for 'cell_expansion'; skip\n", "[8917] no cell mask for 'watershed_only_cyto'; skip\n", "[8917] no cell mask for 'cellpose2chan'; skip\n", "[5191] no cell mask for 'cellpose'; skip\n", "[5191] no cell mask for 'watershed'; skip\n", "[5191] no cell mask for 'cell_expansion'; skip\n", "[5191] no cell mask for 'watershed_only_cyto'; skip\n", "[5191] no cell mask for 'cellpose2chan'; skip\n", "[6833] no cell mask for 'cellpose'; skip\n", "[6833] no cell mask for 'watershed'; skip\n", "[6833] no cell mask for 'cell_expansion'; skip\n", "[6833] no cell mask for 'watershed_only_cyto'; skip\n", "[6833] no cell mask for 'cellpose2chan'; skip\n", "[10166] no cell mask for 'cellpose'; skip\n", "[10166] no cell mask for 'watershed'; skip\n", "[10166] no cell mask for 'cell_expansion'; skip\n", "[10166] no cell mask for 'watershed_only_cyto'; skip\n", "[10166] no cell mask for 'cellpose2chan'; skip\n", "[9106] no cell mask for 'cellpose'; skip\n", "[9106] no cell mask for 'watershed'; skip\n", "[9106] no cell mask for 'cell_expansion'; skip\n", "[9106] no cell mask for 'watershed_only_cyto'; skip\n", "[9106] no cell mask for 'cellpose2chan'; skip\n", "[4642] no cell mask for 'cellpose'; skip\n", "[4642] no cell mask for 'watershed'; skip\n", "[4642] no cell mask for 'cell_expansion'; skip\n", "[4642] no cell mask for 'watershed_only_cyto'; skip\n", "[4642] no cell mask for 'cellpose2chan'; skip\n", "[5792] no cell mask for 'cellpose'; skip\n", "[5792] no cell mask for 'watershed'; skip\n", "[5792] no cell mask for 'cell_expansion'; skip\n", "[5792] no cell mask for 'watershed_only_cyto'; skip\n", "[5792] no cell mask for 'cellpose2chan'; skip\n", "[10061] no cell mask for 'cellpose'; skip\n", "[10061] no cell mask for 'watershed'; skip\n", "[10061] no cell mask for 'cell_expansion'; skip\n", "[10061] no cell mask for 'watershed_only_cyto'; skip\n", "[10061] no cell mask for 'cellpose2chan'; skip\n", "[1110] no cell mask for 'cellpose'; skip\n", "[1110] no cell mask for 'watershed'; skip\n", "[1110] no cell mask for 'cell_expansion'; skip\n", "[1110] no cell mask for 'watershed_only_cyto'; skip\n", "[1110] no cell mask for 'cellpose2chan'; skip\n", "[7925] no cell mask for 'cellpose'; skip\n", "[7925] no cell mask for 'watershed'; skip\n", "[7925] no cell mask for 'cell_expansion'; skip\n", "[7925] no cell mask for 'watershed_only_cyto'; skip\n", "[7925] no cell mask for 'cellpose2chan'; skip\n", "[5863] no cell mask for 'cellpose'; skip\n", "[5863] no cell mask for 'watershed'; skip\n", "[5863] no cell mask for 'cell_expansion'; skip\n", "[5863] no cell mask for 'watershed_only_cyto'; skip\n", "[5863] no cell mask for 'cellpose2chan'; skip\n", "[1112] no cell mask for 'cellpose'; skip\n", "[1112] no cell mask for 'watershed'; skip\n", "[1112] no cell mask for 'cell_expansion'; skip\n", "[1112] no cell mask for 'watershed_only_cyto'; skip\n", "[1112] no cell mask for 'cellpose2chan'; skip\n", "[7739] no cell mask for 'cellpose'; skip\n", "[7739] no cell mask for 'watershed'; skip\n", "[7739] no cell mask for 'cell_expansion'; skip\n", "[7739] no cell mask for 'watershed_only_cyto'; skip\n", "[7739] no cell mask for 'cellpose2chan'; skip\n", "[5059] no cell mask for 'cellpose'; skip\n", "[5059] no cell mask for 'watershed'; skip\n", "[5059] no cell mask for 'cell_expansion'; skip\n", "[5059] no cell mask for 'watershed_only_cyto'; skip\n", "[5059] no cell mask for 'cellpose2chan'; skip\n", "[7113] no cell mask for 'cellpose'; skip\n", "[7113] no cell mask for 'watershed'; skip\n", "[7113] no cell mask for 'cell_expansion'; skip\n", "[7113] no cell mask for 'watershed_only_cyto'; skip\n", "[7113] no cell mask for 'cellpose2chan'; skip\n", "[3569] no cell mask for 'cellpose'; skip\n", "[3569] no cell mask for 'watershed'; skip\n", "[3569] no cell mask for 'cell_expansion'; skip\n", "[3569] no cell mask for 'watershed_only_cyto'; skip\n", "[3569] no cell mask for 'cellpose2chan'; skip\n", "[7685] no cell mask for 'cellpose'; skip\n", "[7685] no cell mask for 'watershed'; skip\n", "[7685] no cell mask for 'cell_expansion'; skip\n", "[7685] no cell mask for 'watershed_only_cyto'; skip\n", "[7685] no cell mask for 'cellpose2chan'; skip\n", "[8224] no cell mask for 'cellpose'; skip\n", "[8224] no cell mask for 'watershed'; skip\n", "[8224] no cell mask for 'cell_expansion'; skip\n", "[8224] no cell mask for 'watershed_only_cyto'; skip\n", "[8224] no cell mask for 'cellpose2chan'; skip\n", "[4515] no cell mask for 'cellpose'; skip\n", "[4515] no cell mask for 'watershed'; skip\n", "[4515] no cell mask for 'cell_expansion'; skip\n", "[4515] no cell mask for 'watershed_only_cyto'; skip\n", "[4515] no cell mask for 'cellpose2chan'; skip\n", "[7870] no cell mask for 'cellpose'; skip\n", "[7870] no cell mask for 'watershed'; skip\n", "[7870] no cell mask for 'cell_expansion'; skip\n", "[7870] no cell mask for 'watershed_only_cyto'; skip\n", "[7870] no cell mask for 'cellpose2chan'; skip\n", "[6790] no cell mask for 'cellpose'; skip\n", "[6790] no cell mask for 'watershed'; skip\n", "[6790] no cell mask for 'cell_expansion'; skip\n", "[6790] no cell mask for 'watershed_only_cyto'; skip\n", "[6790] no cell mask for 'cellpose2chan'; skip\n", "[7962] no cell mask for 'cellpose'; skip\n", "[7962] no cell mask for 'watershed'; skip\n", "[7962] no cell mask for 'cell_expansion'; skip\n", "[7962] no cell mask for 'watershed_only_cyto'; skip\n", "[7962] no cell mask for 'cellpose2chan'; skip\n", "[1111] no cell mask for 'cellpose'; skip\n", "[1111] no cell mask for 'watershed'; skip\n", "[1111] no cell mask for 'cell_expansion'; skip\n", "[1111] no cell mask for 'watershed_only_cyto'; skip\n", "[1111] no cell mask for 'cellpose2chan'; skip\n", "[8746] no cell mask for 'cellpose'; skip\n", "[8746] no cell mask for 'watershed'; skip\n", "[8746] no cell mask for 'cell_expansion'; skip\n", "[8746] no cell mask for 'watershed_only_cyto'; skip\n", "[8746] no cell mask for 'cellpose2chan'; skip\n", "[3532] no cell mask for 'cellpose'; skip\n", "[3532] no cell mask for 'watershed'; skip\n", "[3532] no cell mask for 'cell_expansion'; skip\n", "[3532] no cell mask for 'watershed_only_cyto'; skip\n", "[3532] no cell mask for 'cellpose2chan'; skip\n", "[3999] no cell mask for 'cellpose'; skip\n", "[3999] no cell mask for 'watershed'; skip\n", "[3999] no cell mask for 'cell_expansion'; skip\n", "[3999] no cell mask for 'watershed_only_cyto'; skip\n", "[3999] no cell mask for 'cellpose2chan'; skip\n", "[3527] no cell mask for 'cellpose'; skip\n", "[3527] no cell mask for 'watershed'; skip\n", "[3527] no cell mask for 'cell_expansion'; skip\n", "[3527] no cell mask for 'watershed_only_cyto'; skip\n", "[3527] no cell mask for 'cellpose2chan'; skip\n", "[7071] no cell mask for 'cellpose'; skip\n", "[7071] no cell mask for 'watershed'; skip\n", "[7071] no cell mask for 'cell_expansion'; skip\n", "[7071] no cell mask for 'watershed_only_cyto'; skip\n", "[7071] no cell mask for 'cellpose2chan'; skip\n", "[5923] no cell mask for 'cellpose'; skip\n", "[5923] no cell mask for 'watershed'; skip\n", "[5923] no cell mask for 'cell_expansion'; skip\n", "[5923] no cell mask for 'watershed_only_cyto'; skip\n", "[5923] no cell mask for 'cellpose2chan'; skip\n", "[5789] no cell mask for 'cellpose'; skip\n", "[5789] no cell mask for 'watershed'; skip\n", "[5789] no cell mask for 'cell_expansion'; skip\n", "[5789] no cell mask for 'watershed_only_cyto'; skip\n", "[5789] no cell mask for 'cellpose2chan'; skip\n", "[5794] no cell mask for 'cellpose'; skip\n", "[5794] no cell mask for 'watershed'; skip\n", "[5794] no cell mask for 'cell_expansion'; skip\n", "[5794] no cell mask for 'watershed_only_cyto'; skip\n", "[5794] no cell mask for 'cellpose2chan'; skip\n", "[9755] no cell mask for 'cellpose'; skip\n", "[9755] no cell mask for 'watershed'; skip\n", "[9755] no cell mask for 'cell_expansion'; skip\n", "[9755] no cell mask for 'watershed_only_cyto'; skip\n", "[9755] no cell mask for 'cellpose2chan'; skip\n", "[6020] no cell mask for 'cellpose'; skip\n", "[6020] no cell mask for 'watershed'; skip\n", "[6020] no cell mask for 'cell_expansion'; skip\n", "[6020] no cell mask for 'watershed_only_cyto'; skip\n", "[6020] no cell mask for 'cellpose2chan'; skip\n", "[9170] no cell mask for 'cellpose'; skip\n", "[9170] no cell mask for 'watershed'; skip\n", "[9170] no cell mask for 'cell_expansion'; skip\n", "[9170] no cell mask for 'watershed_only_cyto'; skip\n", "[9170] no cell mask for 'cellpose2chan'; skip\n", "[10015] no cell mask for 'cellpose'; skip\n", "[10015] no cell mask for 'watershed'; skip\n", "[10015] no cell mask for 'cell_expansion'; skip\n", "[10015] no cell mask for 'watershed_only_cyto'; skip\n", "[10015] no cell mask for 'cellpose2chan'; skip\n", "[1116] no cell mask for 'cellpose'; skip\n", "[1116] no cell mask for 'watershed'; skip\n", "[1116] no cell mask for 'cell_expansion'; skip\n", "[1116] no cell mask for 'watershed_only_cyto'; skip\n", "[1116] no cell mask for 'cellpose2chan'; skip\n", "[4238] no cell mask for 'cellpose'; skip\n", "[4238] no cell mask for 'watershed'; skip\n", "[4238] no cell mask for 'cell_expansion'; skip\n", "[4238] no cell mask for 'watershed_only_cyto'; skip\n", "[4238] no cell mask for 'cellpose2chan'; skip\n", "[1114] no cell mask for 'cellpose'; skip\n", "[1114] no cell mask for 'watershed'; skip\n", "[1114] no cell mask for 'cell_expansion'; skip\n", "[1114] no cell mask for 'watershed_only_cyto'; skip\n", "[1114] no cell mask for 'cellpose2chan'; skip\n", "[5795] no cell mask for 'cellpose'; skip\n", "[5795] no cell mask for 'watershed'; skip\n", "[5795] no cell mask for 'cell_expansion'; skip\n", "[5795] no cell mask for 'watershed_only_cyto'; skip\n", "[5795] no cell mask for 'cellpose2chan'; skip\n", "[4548] no cell mask for 'cellpose'; skip\n", "[4548] no cell mask for 'watershed'; skip\n", "[4548] no cell mask for 'cell_expansion'; skip\n", "[4548] no cell mask for 'watershed_only_cyto'; skip\n", "[4548] no cell mask for 'cellpose2chan'; skip\n", "[8517] no cell mask for 'cellpose'; skip\n", "[8517] no cell mask for 'watershed'; skip\n", "[8517] no cell mask for 'cell_expansion'; skip\n", "[8517] no cell mask for 'watershed_only_cyto'; skip\n", "[8517] no cell mask for 'cellpose2chan'; skip\n", "[9783] no cell mask for 'cellpose'; skip\n", "[9783] no cell mask for 'watershed'; skip\n", "[9783] no cell mask for 'cell_expansion'; skip\n", "[9783] no cell mask for 'watershed_only_cyto'; skip\n", "[9783] no cell mask for 'cellpose2chan'; skip\n", "[1108] no cell mask for 'cellpose'; skip\n", "[1108] no cell mask for 'watershed'; skip\n", "[1108] no cell mask for 'cell_expansion'; skip\n", "[1108] no cell mask for 'watershed_only_cyto'; skip\n", "[1108] no cell mask for 'cellpose2chan'; skip\n", "[8310] no cell mask for 'cellpose'; skip\n", "[8310] no cell mask for 'watershed'; skip\n", "[8310] no cell mask for 'cell_expansion'; skip\n", "[8310] no cell mask for 'watershed_only_cyto'; skip\n", "[8310] no cell mask for 'cellpose2chan'; skip\n", "[9472] no cell mask for 'cellpose'; skip\n", "[9472] no cell mask for 'watershed'; skip\n", "[9472] no cell mask for 'cell_expansion'; skip\n", "[9472] no cell mask for 'watershed_only_cyto'; skip\n", "[9472] no cell mask for 'cellpose2chan'; skip\n", "[4736] no cell mask for 'cellpose'; skip\n", "[4736] no cell mask for 'watershed'; skip\n", "[4736] no cell mask for 'cell_expansion'; skip\n", "[4736] no cell mask for 'watershed_only_cyto'; skip\n", "[4736] no cell mask for 'cellpose2chan'; skip\n", "[7319] no cell mask for 'cellpose'; skip\n", "[7319] no cell mask for 'watershed'; skip\n", "[7319] no cell mask for 'cell_expansion'; skip\n", "[7319] no cell mask for 'watershed_only_cyto'; skip\n", "[7319] no cell mask for 'cellpose2chan'; skip\n", "[9866] no cell mask for 'cellpose'; skip\n", "[9866] no cell mask for 'watershed'; skip\n", "[9866] no cell mask for 'cell_expansion'; skip\n", "[9866] no cell mask for 'watershed_only_cyto'; skip\n", "[9866] no cell mask for 'cellpose2chan'; skip\n", "[6212] no cell mask for 'cellpose'; skip\n", "[6212] no cell mask for 'watershed'; skip\n", "[6212] no cell mask for 'cell_expansion'; skip\n", "[6212] no cell mask for 'watershed_only_cyto'; skip\n", "[6212] no cell mask for 'cellpose2chan'; skip\n", "[8942] no cell mask for 'cellpose'; skip\n", "[8942] no cell mask for 'watershed'; skip\n", "[8942] no cell mask for 'cell_expansion'; skip\n", "[8942] no cell mask for 'watershed_only_cyto'; skip\n", "[8942] no cell mask for 'cellpose2chan'; skip\n", "[1113] no cell mask for 'cellpose'; skip\n", "[1113] no cell mask for 'watershed'; skip\n", "[1113] no cell mask for 'cell_expansion'; skip\n", "[1113] no cell mask for 'watershed_only_cyto'; skip\n", "[1113] no cell mask for 'cellpose2chan'; skip\n", "[6748] no cell mask for 'cellpose'; skip\n", "[6748] no cell mask for 'watershed'; skip\n", "[6748] no cell mask for 'cell_expansion'; skip\n", "[6748] no cell mask for 'watershed_only_cyto'; skip\n", "[6748] no cell mask for 'cellpose2chan'; skip\n", "[1115] no cell mask for 'cellpose'; skip\n", "[1115] no cell mask for 'watershed'; skip\n", "[1115] no cell mask for 'cell_expansion'; skip\n", "[1115] no cell mask for 'watershed_only_cyto'; skip\n", "[1115] no cell mask for 'cellpose2chan'; skip\n", "[5410] no cell mask for 'cellpose'; skip\n", "[5410] no cell mask for 'watershed'; skip\n", "[5410] no cell mask for 'cell_expansion'; skip\n", "[5410] no cell mask for 'watershed_only_cyto'; skip\n", "[5410] no cell mask for 'cellpose2chan'; skip\n", "[4201] no cell mask for 'cellpose'; skip\n", "[4201] no cell mask for 'watershed'; skip\n", "[4201] no cell mask for 'cell_expansion'; skip\n", "[4201] no cell mask for 'watershed_only_cyto'; skip\n", "[4201] no cell mask for 'cellpose2chan'; skip\n", "[6523] no cell mask for 'cellpose'; skip\n", "[6523] no cell mask for 'watershed'; skip\n", "[6523] no cell mask for 'cell_expansion'; skip\n", "[6523] no cell mask for 'watershed_only_cyto'; skip\n", "[6523] no cell mask for 'cellpose2chan'; skip\n", "[6466] no cell mask for 'cellpose'; skip\n", "[6466] no cell mask for 'watershed'; skip\n", "[6466] no cell mask for 'cell_expansion'; skip\n", "[6466] no cell mask for 'watershed_only_cyto'; skip\n", "[6466] no cell mask for 'cellpose2chan'; skip\n", "[4683] no cell mask for 'cellpose'; skip\n", "[4683] no cell mask for 'watershed'; skip\n", "[4683] no cell mask for 'cell_expansion'; skip\n", "[4683] no cell mask for 'watershed_only_cyto'; skip\n", "[4683] no cell mask for 'cellpose2chan'; skip\n", "[4319] no cell mask for 'cellpose'; skip\n", "[4319] no cell mask for 'watershed'; skip\n", "[4319] no cell mask for 'cell_expansion'; skip\n", "[4319] no cell mask for 'watershed_only_cyto'; skip\n", "[4319] no cell mask for 'cellpose2chan'; skip\n", "[1109] no cell mask for 'cellpose'; skip\n", "[1109] no cell mask for 'watershed'; skip\n", "[1109] no cell mask for 'cell_expansion'; skip\n", "[1109] no cell mask for 'watershed_only_cyto'; skip\n", "[1109] no cell mask for 'cellpose2chan'; skip\n", "  method   iou  precision\n", "0   cyto  0.50   0.755568\n", "1   cyto  0.55   0.711342\n", "2   cyto  0.60   0.656969\n", "3   cyto  0.65   0.587028\n", "4   cyto  0.70   0.496387\n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Assume `dataset` is an instance of YourDatasetClass\n", "df_nuc   = dataset.evaluate_nuclei()\n", "df_cells = dataset.evaluate_cell()\n", "\n", "# Quick peek\n", "print(df_nuc.head())\n", "print(df_cells.head())"]}, {"cell_type": "code", "execution_count": 18, "id": "83d3281a", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "plt.style.use('default')\n", "def plot_precision_iou(df,\n", "                       legend_mapping=None,\n", "                       ax=None,\n", "                       title=\"Mean Precision vs. IoU Threshold\"):\n", "    \"\"\"\n", "    Parameters\n", "    ----------\n", "    df : pd.DataFrame\n", "        Must have columns ['method', 'iou', 'precision'].\n", "    legend_mapping : dict or None\n", "        Optional pretty-name mapping for each method.\n", "    ax : matplotlib.axes.Axes or None\n", "        Where to draw.  If None, a new figure is created.\n", "    \"\"\"\n", "    if ax is None:\n", "        plt.figure(figsize=(6, 4))\n", "        ax = plt.gca()\n", "\n", "    legend_mapping = legend_mapping or {}           # fall-back to identity\n", "\n", "    for method, sub in df.groupby(\"method\"):\n", "        # sub already holds *mean precision* per IoU (because evaluate_nuclei\n", "        # aggregated across samples).  Ensure it's sorted by IoU:\n", "        sub = sub.sort_values(\"iou\")\n", "\n", "        # mAP = mean over IoU thresholds\n", "        mAP = sub[\"precision\"].mean()\n", "\n", "        label = f\"{legend_mapping.get(method, method)} (mAP={mAP:.2f})\"\n", "        ax.plot(sub[\"iou\"], sub[\"precision\"],\n", "                marker=\"o\", linestyle=\"-\", label=label)\n", "\n", "    ax.set(title=title,\n", "           xlabel=\"IoU Threshold\",\n", "           ylabel=\"Mean Precision\")\n", "    ax.legend()\n", "    plt.tight_layout()\n", "    return ax"]}, {"cell_type": "code", "execution_count": 19, "id": "a4d216e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: title={'center': 'Mean Precision vs. IoU Threshold'}, xlabel='IoU Threshold', ylabel='Mean Precision'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_precision_iou(df_nuc[df_nuc[\"method\"]!=\"watershed\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5111cf97", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ifimage", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}