{"cells": [{"cell_type": "code", "execution_count": 19, "id": "08092063", "metadata": {}, "outputs": [], "source": ["import importlib\n", "import ifimage_tools\n", "importlib.reload(ifimage_tools)\n", "import pandas as pd\n", "import warnings\n", "import pickle"]}, {"cell_type": "code", "execution_count": 28, "id": "5d9a6566", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "image_dir = \"/home/<USER>/ifimage/Reorgnized Ground Truth\"\n", "dataset = ifimage_tools.IfImageDataset(image_dir, \"/home/<USER>/ifimage/old_cellpose/merged_mask\", {})\n", "dataset.load_data()\n", "old_version_sample_ids = [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]\n", "for sample_id in old_version_sample_ids:\n", "    if sample_id in dataset.samples:\n", "        del dataset.samples[sample_id]"]}, {"cell_type": "code", "execution_count": 21, "id": "a3bb0952", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running Cellpose 'cyto3'…\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved cyto3 → mask/5803/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12781/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12779/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12786/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/10594/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8408jan22/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/15591/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12793/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/15972/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12794/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1120/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12795/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12792/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12787/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12780/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/2529/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8265/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/15973/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12798/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8407jan22/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8406jan22/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12799/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8405jan22/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/15970/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12791/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12784/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/15968/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12789/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12783/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12796/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/15969/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12797/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12782/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/15971/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12785/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12790/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/12788/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8409/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6390v2/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8550/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8917/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5191/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6833/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/10166/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/9106/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4642/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5792/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/10061/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1110/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7925/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5863/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1112/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7739/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5059/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7113/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/3569/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7685/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8224/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4515/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7870/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6790/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7962/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1111/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8746/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/3532/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/3999/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/3527/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7071/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5923/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5789/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5794/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/9755/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6020/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/9170/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/10015/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1116/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4238/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1114/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5795/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4548/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8517/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/9783/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1108/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8310/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/9472/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4736/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/7319/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/9866/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6212/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/8942/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1113/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6748/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1115/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/5410/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4201/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6523/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/6466/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4683/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/4319/cyto3.npy\n", "Running Cellpose 'cyto3'…\n", "Saved cyto3 → mask/1109/cyto3.npy\n"]}], "source": ["output_root = \"mask\"\n", "os.makedirs(output_root, exist_ok=True)\n", "\n", "for sample_id, sample in dataset.samples.items():\n", "    # 跑 nucleus segmentation pipeline\n", "    sample.apply_nuc_pipeline()\n", "    \n", "    # 给每个 sample 创建自己的子文件夹：mask/<sample_id>/\n", "    sample_dir = os.path.join(output_root, sample_id)\n", "    os.makedirs(sample_dir, exist_ok=True)\n", "    \n", "    # 遍历 sample.masks，把不为空的 mask 存成 .npy\n", "    for mask_name, mask_array in sample.masks.items():\n", "        if mask_array is None:\n", "            continue\n", "        out_path = os.path.join(sample_dir, f\"{mask_name}.npy\")\n", "        np.save(out_path, mask_array)\n", "        print(f\"Saved {mask_name} → {out_path}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "975ad14f", "metadata": {}, "outputs": [], "source": ["from cellpose import models as _cp_models\n", "def _no_size(*a, **k):  # short-circuit downloader\n", "    return None\n", "_cp_models.size_model_path = _no_size\n", "\n", "model_cp = _cp_models.Cellpose(gpu=True, model_type='cyto3')"]}, {"cell_type": "code", "execution_count": null, "id": "9d7c099a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cellpose_old", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}