{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2b9e5636", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Welcome to CellposeSAM, cellpose v\n", "cellpose version: \t4.0.4 \n", "platform:       \tlinux \n", "python version: \t3.10.16 \n", "torch version:  \t2.7.0+cu126! The neural network component of\n", "CPSAM is much larger than in previous versions and CPU excution is slow. \n", "We encourage users to use GPU/MPS if available. \n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-09 07:18:03.901614: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2025-06-09 07:18:03.950698: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2025-06-09 07:18:05.607181: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "auto.py (21): IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n"]}], "source": ["\n", "import importlib\n", "import ifimage_tools\n", "importlib.reload(ifimage_tools)\n", "import pandas as pd\n", "import warnings\n", "import pickle\n", "\n", "import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "image_dir = \"/home/<USER>/ifimage/Reorgnized Ground Truth\"\n", "dataset = ifimage_tools.IfImageDataset(image_dir, \"/home/<USER>/ifimage/merged_mask\",\"/home/<USER>/ifimage/cell_masks\", {})\n", "dataset.load_data()\n", "old_version_sample_ids = [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]\n", "for sample_id in old_version_sample_ids:\n", "    if sample_id in dataset.samples:\n", "        del dataset.samples[sample_id]"]}, {"cell_type": "code", "execution_count": 2, "id": "b44547d3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["cell eval:   0%|          | 0/100 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [5803/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5803/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:   8%|▊         | 8/100 [00:28<05:40,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [15972/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [15972/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  43%|████▎     | 43/100 [02:21<01:20,  1.41s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [8550/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8550/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8917/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5191/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6833/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10166/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9106/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4642/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  51%|█████     | 51/100 [02:21<00:22,  2.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [5792/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5792/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10061/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1110/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7925/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5863/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1112/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7739/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5059/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  59%|█████▉    | 59/100 [02:21<00:08,  4.84it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [7113/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7113/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3569/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7685/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8224/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4515/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7870/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6790/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  67%|██████▋   | 67/100 [02:21<00:03,  9.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [7962/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7962/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1111/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8746/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3532/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3999/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [3527/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7071/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5923/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  75%|███████▌  | 75/100 [02:22<00:01, 15.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [5789/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5789/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5794/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9755/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6020/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9170/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [10015/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1116/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  83%|████████▎ | 83/100 [02:22<00:00, 21.96it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [4238/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4238/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1114/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5795/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4548/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8517/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9783/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1108/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8310/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  91%|█████████ | 91/100 [02:22<00:00, 27.90it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [9472/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9472/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4736/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [7319/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [9866/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6212/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [8942/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1113/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval:  99%|█████████▉| 99/100 [02:22<00:00, 32.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [6748/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6748/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1115/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [5410/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4201/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6523/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [6466/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4683/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [4319/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell eval: 100%|██████████| 100/100 [02:22<00:00,  1.43s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["matching() error [1109/cell_expansion/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cell_expansion/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/watershed_only_cyto/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.5]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.55]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.6000000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.6500000000000001]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.7000000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.7500000000000002]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.8000000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.8500000000000003]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.9000000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "matching() error [1109/cellpose2chan/0.9500000000000004]: y_true ((1024, 1388)) and y_pred ((1040, 1388)) have different shapes\n", "     method   iou  precision\n", "0  cellpose  0.50   0.062060\n", "1  cellpose  0.55   0.053997\n", "2  cellpose  0.60   0.046234\n", "3  cellpose  0.65   0.036746\n", "4  cellpose  0.70   0.029776\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Assume `dataset` is an instance of YourDatasetClass\n", "#df_nuc   = dataset.evaluate_nuclei()\n", "df_cells = dataset.evaluate_cell()\n", "\n", "# Quick peek\n", "#print(df_nuc.head())\n", "print(df_cells.head())"]}, {"cell_type": "code", "execution_count": 3, "id": "83d3281a", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "plt.style.use('default')\n", "def plot_precision_iou(df,\n", "                       legend_mapping=None,\n", "                       ax=None,\n", "                       title=\"Mean Precision vs. IoU Threshold\"):\n", "    \"\"\"\n", "    Parameters\n", "    ----------\n", "    df : pd.DataFrame\n", "        Must have columns ['method', 'iou', 'precision'].\n", "    legend_mapping : dict or None\n", "        Optional pretty-name mapping for each method.\n", "    ax : matplotlib.axes.Axes or None\n", "        Where to draw.  If None, a new figure is created.\n", "    \"\"\"\n", "    if ax is None:\n", "        plt.figure(figsize=(6, 4))\n", "        ax = plt.gca()\n", "\n", "    legend_mapping = legend_mapping or {}           # fall-back to identity\n", "\n", "    for method, sub in df.groupby(\"method\"):\n", "        # sub already holds *mean precision* per IoU (because evaluate_nuclei\n", "        # aggregated across samples).  Ensure it's sorted by IoU:\n", "        sub = sub.sort_values(\"iou\")\n", "\n", "        # mAP = mean over IoU thresholds\n", "        mAP = sub[\"precision\"].mean()\n", "\n", "        label = f\"{legend_mapping.get(method, method)} (mAP={mAP:.2f})\"\n", "        ax.plot(sub[\"iou\"], sub[\"precision\"],\n", "                marker=\"o\", linestyle=\"-\", label=label)\n", "\n", "    ax.set(title=title,\n", "           xlabel=\"IoU Threshold\",\n", "           ylabel=\"Mean Precision\")\n", "    ax.legend()\n", "    plt.tight_layout()\n", "    return ax"]}, {"cell_type": "code", "execution_count": null, "id": "5a392d1f", "metadata": {}, "outputs": [], "source": ["plot_precision_iou(df_cells[df_cells[\"method\"]!=\"watershed\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5111cf97", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ifimage", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}