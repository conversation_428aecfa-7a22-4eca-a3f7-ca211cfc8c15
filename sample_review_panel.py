#!/usr/bin/env python3
"""
Sample Review Panel for Manual Verification

This script creates an interactive visual panel to review all samples and verify
that DAPI channels, marker channels, and masks are correctly matched.

Uses static methods from ifimage_tools.py for visualization.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from pathlib import Path
import pandas as pd
from ifimage_tools import IfImageDataset, safe_read, draw_overlap, CentroidAnalyzer
import skimage.io as skio
from matplotlib.widgets import Button
import warnings
warnings.filterwarnings('ignore')

class SampleReviewPanel:
    def __init__(self, image_dir, masks_dir=None, manual_cell_counts=None):
        """
        Initialize the review panel
        
        Args:
            image_dir: Directory containing image files
            masks_dir: Directory containing mask files (can be same as image_dir)
            manual_cell_counts: Dictionary of manual cell counts
        """
        self.image_dir = image_dir
        self.masks_dir = masks_dir or image_dir
        self.manual_cell_counts = manual_cell_counts or {}
        
        # Load dataset
        print("Loading dataset...")
        self.dataset = IfImageDataset(
            image_dir=self.image_dir,
            nuclei_masks_dir=self.masks_dir,
            cell_masks_dir=self.masks_dir,
            manual_cell_counts=self.manual_cell_counts
        )
        self.dataset.load_data()
        
        # Get sample list
        self.sample_ids = list(self.dataset.samples.keys())
        self.current_index = 0
        
        print(f"Loaded {len(self.sample_ids)} samples")
        
    def create_summary_table(self):
        """Create a summary table of all samples and their components"""
        print("\n" + "="*100)
        print("DATASET SUMMARY")
        print("="*100)
        
        # Use the existing summary method
        self.dataset.summary(table=True)
        
        # Create detailed DataFrame for analysis
        data = []
        for sample_id, sample in self.dataset.samples.items():
            row = {
                'sample_id': sample_id,
                'celltype': sample.celltype or 'Unknown',
                'has_dapi': sample.dapi is not None,
                'has_marker': sample.marker is not None,
                'has_dapi_mask': sample.dapi_multi_mask is not None,
                'has_cellbodies': sample.cellbodies_multimask is not None,
                'dapi_shape': sample.dapi.shape if sample.dapi is not None else None,
                'marker_shape': sample.marker.shape if sample.marker is not None else None,
                'dapi_mask_max': sample.dapi_multi_mask.max() if sample.dapi_multi_mask is not None else 0,
                'cellbodies_max': sample.cellbodies_multimask.max() if sample.cellbodies_multimask is not None else 0,
                'manual_count': sample.manual_cell_count
            }
            data.append(row)
        
        self.summary_df = pd.DataFrame(data)
        return self.summary_df
    
    @staticmethod
    def visualize_multimask(multi_mask, ax, title="Multi-mask"):
        """Visualize multi-valued mask with different colors for each region"""
        if multi_mask is None:
            ax.text(0.5, 0.5, 'No Mask', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            return
            
        unique_masks = np.unique(multi_mask)
        unique_masks = unique_masks[unique_masks != 0]
        num_masks = len(unique_masks)
        
        if num_masks == 0:
            ax.text(0.5, 0.5, 'Empty Mask', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f"{title} (Empty)")
            return
            
        cmap = plt.get_cmap('gist_ncar', num_masks)
        colored_mask = np.zeros((*multi_mask.shape, 3))
        
        for i, mask_id in enumerate(unique_masks):
            color = cmap(i)[:3]
            colored_mask[multi_mask == mask_id] = color
            
        ax.imshow(colored_mask)
        ax.set_title(f"{title} ({num_masks} regions)")
        ax.set_xticks([])
        ax.set_yticks([])
    
    def display_sample(self, sample_id):
        """Display a comprehensive view of a single sample"""
        if sample_id not in self.dataset.samples:
            print(f"Sample {sample_id} not found!")
            return
            
        sample = self.dataset.samples[sample_id]
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Sample Review: {sample.celltype}_{sample_id}', fontsize=16, fontweight='bold')
        
        # Row 1: Raw images
        # DAPI channel
        if sample.dapi is not None:
            dapi_img = sample.dapi[..., 0] if sample.dapi.ndim == 3 else sample.dapi
            axes[0, 0].imshow(dapi_img, cmap='gray')
            axes[0, 0].set_title(f'DAPI Channel\nShape: {dapi_img.shape}')
        else:
            axes[0, 0].text(0.5, 0.5, 'No DAPI', ha='center', va='center', transform=axes[0, 0].transAxes)
            axes[0, 0].set_title('DAPI Channel (Missing)')
        axes[0, 0].set_xticks([])
        axes[0, 0].set_yticks([])
        
        # Marker channel
        if sample.marker is not None:
            marker_img = sample.marker[..., 0] if sample.marker.ndim == 3 else sample.marker
            axes[0, 1].imshow(marker_img, cmap='hot')
            axes[0, 1].set_title(f'Marker Channel\nShape: {marker_img.shape}')
        else:
            axes[0, 1].text(0.5, 0.5, 'No Marker', ha='center', va='center', transform=axes[0, 1].transAxes)
            axes[0, 1].set_title('Marker Channel (Missing)')
        axes[0, 1].set_xticks([])
        axes[0, 1].set_yticks([])
        
        # Overlay
        if sample.dapi is not None and sample.marker is not None:
            dapi_norm = dapi_img / dapi_img.max() if dapi_img.max() > 0 else dapi_img
            marker_norm = marker_img / marker_img.max() if marker_img.max() > 0 else marker_img
            
            overlay = np.zeros((*dapi_img.shape, 3))
            overlay[..., 0] = marker_norm  # Red channel for marker
            overlay[..., 2] = dapi_norm    # Blue channel for DAPI
            
            axes[0, 2].imshow(overlay)
            axes[0, 2].set_title('DAPI + Marker Overlay\n(Blue: DAPI, Red: Marker)')
        else:
            axes[0, 2].text(0.5, 0.5, 'Cannot create overlay', ha='center', va='center', transform=axes[0, 2].transAxes)
            axes[0, 2].set_title('Overlay (Missing data)')
        axes[0, 2].set_xticks([])
        axes[0, 2].set_yticks([])
        
        # Row 2: Masks
        # DAPI mask
        self.visualize_multimask(sample.dapi_multi_mask, axes[1, 0], "DAPI Multi-mask")
        
        # Cell bodies mask
        self.visualize_multimask(sample.cellbodies_multimask, axes[1, 1], "Cell Bodies Multi-mask")
        
        # Mask overlay comparison
        if sample.dapi_multi_mask is not None and sample.cellbodies_multimask is not None:
            draw_overlap(sample.cellbodies_multimask, sample.dapi_multi_mask, 
                        ax=axes[1, 2], title="Mask Comparison\n(Red: Cell Bodies, Green: DAPI, Yellow: Overlap)")
        else:
            axes[1, 2].text(0.5, 0.5, 'Cannot compare masks', ha='center', va='center', transform=axes[1, 2].transAxes)
            axes[1, 2].set_title('Mask Comparison (Missing data)')
            axes[1, 2].set_xticks([])
            axes[1, 2].set_yticks([])
        
        # Add sample statistics as text
        stats_text = self._get_sample_stats(sample)
        fig.text(0.02, 0.02, stats_text, fontsize=10, verticalalignment='bottom', 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        
        plt.tight_layout()
        plt.subplots_adjust(bottom=0.15)
        plt.show()
        
        return fig
    
    def _get_sample_stats(self, sample):
        """Get formatted statistics for a sample"""
        stats = [
            f"Sample ID: {sample.sample_id}",
            f"Cell Type: {sample.celltype or 'Unknown'}",
            f"Manual Count: {sample.manual_cell_count or 'N/A'}",
        ]
        
        if sample.dapi_multi_mask is not None:
            stats.append(f"DAPI Regions: {sample.dapi_multi_mask.max()}")
        else:
            stats.append("DAPI Regions: N/A")
            
        if sample.cellbodies_multimask is not None:
            stats.append(f"Cell Body Regions: {sample.cellbodies_multimask.max()}")
        else:
            stats.append("Cell Body Regions: N/A")
            
        return "\n".join(stats)
    
    def review_all_samples(self, samples_per_page=6):
        """Display multiple samples in a grid for quick review"""
        total_samples = len(self.sample_ids)
        
        for start_idx in range(0, total_samples, samples_per_page):
            end_idx = min(start_idx + samples_per_page, total_samples)
            current_samples = self.sample_ids[start_idx:end_idx]
            
            # Create grid layout
            rows = 2
            cols = min(3, len(current_samples))
            if len(current_samples) > 3:
                rows = 2
                cols = 3
            
            fig, axes = plt.subplots(rows, cols, figsize=(20, 12))
            if rows == 1:
                axes = axes.reshape(1, -1)
            
            fig.suptitle(f'Sample Overview: {start_idx+1}-{end_idx} of {total_samples}', 
                        fontsize=16, fontweight='bold')
            
            for i, sample_id in enumerate(current_samples):
                row = i // cols
                col = i % cols
                ax = axes[row, col] if rows > 1 else axes[col]
                
                sample = self.dataset.samples[sample_id]
                
                # Create a summary visualization
                if sample.dapi is not None and sample.marker is not None:
                    dapi_img = sample.dapi[..., 0] if sample.dapi.ndim == 3 else sample.dapi
                    marker_img = sample.marker[..., 0] if sample.marker.ndim == 3 else sample.marker
                    
                    # Normalize and create overlay
                    dapi_norm = dapi_img / dapi_img.max() if dapi_img.max() > 0 else dapi_img
                    marker_norm = marker_img / marker_img.max() if marker_img.max() > 0 else marker_img
                    
                    overlay = np.zeros((*dapi_img.shape, 3))
                    overlay[..., 0] = marker_norm
                    overlay[..., 2] = dapi_norm
                    
                    ax.imshow(overlay)
                else:
                    ax.text(0.5, 0.5, 'Missing Data', ha='center', va='center', transform=ax.transAxes)
                
                # Add sample info
                title = f"{sample.celltype}_{sample_id}"
                if sample.cellbodies_multimask is not None:
                    title += f"\nCells: {sample.cellbodies_multimask.max()}"
                else:
                    title += "\nCells: N/A"
                    
                ax.set_title(title, fontsize=10)
                ax.set_xticks([])
                ax.set_yticks([])
            
            # Hide unused subplots
            for i in range(len(current_samples), rows * cols):
                row = i // cols
                col = i % cols
                ax = axes[row, col] if rows > 1 else axes[col]
                ax.set_visible(False)
            
            plt.tight_layout()
            plt.show()
            
            # Ask user if they want to continue
            if end_idx < total_samples:
                response = input(f"\nShowing samples {start_idx+1}-{end_idx}. Continue to next batch? (y/n): ")
                if response.lower() != 'y':
                    break
    
    def interactive_review(self):
        """Start an interactive review session"""
        print("\n" + "="*60)
        print("INTERACTIVE SAMPLE REVIEW")
        print("="*60)
        print("Commands:")
        print("  'next' or 'n' - Next sample")
        print("  'prev' or 'p' - Previous sample")
        print("  'goto <id>' - Go to specific sample ID")
        print("  'list' - Show sample list")
        print("  'summary' - Show dataset summary")
        print("  'overview' - Show overview grid")
        print("  'quit' or 'q' - Exit")
        print("-"*60)
        
        while True:
            current_sample_id = self.sample_ids[self.current_index]
            print(f"\nCurrent: {self.current_index + 1}/{len(self.sample_ids)} - {current_sample_id}")
            
            command = input("Enter command: ").strip().lower()
            
            if command in ['quit', 'q']:
                break
            elif command in ['next', 'n']:
                self.current_index = (self.current_index + 1) % len(self.sample_ids)
                self.display_sample(self.sample_ids[self.current_index])
            elif command in ['prev', 'p']:
                self.current_index = (self.current_index - 1) % len(self.sample_ids)
                self.display_sample(self.sample_ids[self.current_index])
            elif command.startswith('goto'):
                try:
                    target_id = command.split()[1]
                    if target_id in self.sample_ids:
                        self.current_index = self.sample_ids.index(target_id)
                        self.display_sample(target_id)
                    else:
                        print(f"Sample {target_id} not found!")
                except IndexError:
                    print("Usage: goto <sample_id>")
            elif command == 'list':
                print("\nAvailable samples:")
                for i, sid in enumerate(self.sample_ids):
                    marker = ">>> " if i == self.current_index else "    "
                    sample = self.dataset.samples[sid]
                    print(f"{marker}{i+1:3d}. {sample.celltype}_{sid}")
            elif command == 'summary':
                self.create_summary_table()
            elif command == 'overview':
                self.review_all_samples()
            elif command == '':
                # Default action - show current sample
                self.display_sample(current_sample_id)
            else:
                print("Unknown command. Type 'quit' to exit.")

def main():
    """Main function to start the review panel"""
    # You can modify these paths as needed
    image_dir = "processed_dataset/images"  # or your actual image directory
    masks_dir = "processed_dataset/masks"   # or your actual masks directory
    
    # Check if directories exist
    if not os.path.exists(image_dir):
        print(f"Image directory not found: {image_dir}")
        print("Please update the paths in the main() function")
        return
    
    if not os.path.exists(masks_dir):
        print(f"Masks directory not found: {masks_dir}")
        print("Please update the paths in the main() function")
        return
    
    # Create review panel
    panel = SampleReviewPanel(image_dir, masks_dir)
    
    # Show summary first
    panel.create_summary_table()
    
    # Start interactive review
    panel.interactive_review()

if __name__ == "__main__":
    main()
