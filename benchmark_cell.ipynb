{"cells": [{"cell_type": "code", "execution_count": 3, "id": "08092063", "metadata": {}, "outputs": [], "source": ["import importlib, os, numpy as np\n", "import ifimage_tools                           # 你自己的 utils 包\n", "importlib.reload(ifimage_tools)                # 热重载\n", "from tqdm.auto import tqdm                     # 可选：进度条\n", "import os\n", "import numpy as np\n", "from stardist.matching import matching"]}, {"cell_type": "code", "execution_count": 4, "id": "5d9a6566", "metadata": {}, "outputs": [], "source": ["image_dir  = \"Reorgnized Ground Truth\"\n", "masks_dir  = \"merged_mask\"\n", "dataset = ifimage_tools.IfImageDataset(image_dir, masks_dir, {})\n", "dataset.load_data()\n", "\n", "for sid in [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]:\n", "    dataset.samples.pop(sid, None)\n", "\n", "# --------------------------- 2. 分割 & 保存 -----------------------------------\n", "output_root = \"cell_masks\"            # 新目录，不要跟 nuclei 混\n", "os.makedirs(output_root, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "9ab78f31", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5803: marker ndim=2, shape=(1040, 1388), min=10.000, max=255.000\n"]}], "source": ["for sid, sample in dataset.samples.items():\n", "    if sample.marker is None: continue\n", "    m = sample.marker\n", "    print(f\"{sid}: marker ndim={m.ndim}, shape={m.shape}, min={m.min():.3f}, max={m.max():.3f}\")\n", "    break"]}, {"cell_type": "code", "execution_count": 6, "id": "7d7a948a", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_items([('5803', <ifimage_tools.ImageSample object at 0x7f5c69d7b370>), ('12781', <ifimage_tools.ImageSample object at 0x7f5c69d3a8c0>), ('12779', <ifimage_tools.ImageSample object at 0x7f5c69d7a890>), ('12786', <ifimage_tools.ImageSample object at 0x7f5c69dc8790>), ('10594', <ifimage_tools.ImageSample object at 0x7f5c69d92bc0>), ('8408jan22', <ifimage_tools.ImageSample object at 0x7f5c69d93e80>), ('15591', <ifimage_tools.ImageSample object at 0x7f5c69d78100>), ('12793', <ifimage_tools.ImageSample object at 0x7f5ede641780>), ('15972', <ifimage_tools.ImageSample object at 0x7f5c69d7a770>), ('12794', <ifimage_tools.ImageSample object at 0x7f5c6a2b3490>), ('1120', <ifimage_tools.ImageSample object at 0x7f5ede641750>), ('12795', <ifimage_tools.ImageSample object at 0x7f5ede640e50>), ('12792', <ifimage_tools.ImageSample object at 0x7f5ede640fa0>), ('12787', <ifimage_tools.ImageSample object at 0x7f5ede640ca0>), ('12780', <ifimage_tools.ImageSample object at 0x7f5c6a2b36d0>), ('2529', <ifimage_tools.ImageSample object at 0x7f5c69b62590>), ('8265', <ifimage_tools.ImageSample object at 0x7f5c69b60cd0>), ('15973', <ifimage_tools.ImageSample object at 0x7f5c69b63220>), ('12798', <ifimage_tools.ImageSample object at 0x7f5c69d93640>), ('8407jan22', <ifimage_tools.ImageSample object at 0x7f5c6a2b3010>), ('8406jan22', <ifimage_tools.ImageSample object at 0x7f5c6a2b3970>), ('12799', <ifimage_tools.ImageSample object at 0x7f5c69b62ad0>), ('8405jan22', <ifimage_tools.ImageSample object at 0x7f5c6a2b34f0>), ('15970', <ifimage_tools.ImageSample object at 0x7f5c6a2b3c70>), ('12791', <ifimage_tools.ImageSample object at 0x7f5c69df3d30>), ('12784', <ifimage_tools.ImageSample object at 0x7f5c69df3f70>), ('15968', <ifimage_tools.ImageSample object at 0x7f5c69df2bf0>), ('12789', <ifimage_tools.ImageSample object at 0x7f5c69df1de0>), ('12783', <ifimage_tools.ImageSample object at 0x7f5c6a2b39a0>), ('12796', <ifimage_tools.ImageSample object at 0x7f5c6a2b3940>), ('15969', <ifimage_tools.ImageSample object at 0x7f5c69b630a0>), ('12797', <ifimage_tools.ImageSample object at 0x7f5c69df0f70>), ('12782', <ifimage_tools.ImageSample object at 0x7f5c6a2b2e30>), ('15971', <ifimage_tools.ImageSample object at 0x7f5c6a2b3370>), ('12785', <ifimage_tools.ImageSample object at 0x7f5c69b63ca0>), ('12790', <ifimage_tools.ImageSample object at 0x7f5c6a2b3a30>), ('12788', <ifimage_tools.ImageSample object at 0x7f5c69b62e00>), ('8409', <ifimage_tools.ImageSample object at 0x7f5c69b62920>), ('6390v2', <ifimage_tools.ImageSample object at 0x7f5c69df1210>), ('8550', <ifimage_tools.ImageSample object at 0x7f5c69b62a40>), ('8917', <ifimage_tools.ImageSample object at 0x7f5c69df18a0>), ('5191', <ifimage_tools.ImageSample object at 0x7f5c69df01c0>), ('6833', <ifimage_tools.ImageSample object at 0x7f5c69b60430>), ('10166', <ifimage_tools.ImageSample object at 0x7f5c69a17e80>), ('9106', <ifimage_tools.ImageSample object at 0x7f5c6a2b3280>), ('4642', <ifimage_tools.ImageSample object at 0x7f5c69a17e50>), ('5792', <ifimage_tools.ImageSample object at 0x7f5c69a17d60>), ('10061', <ifimage_tools.ImageSample object at 0x7f5c69df1c60>), ('1110', <ifimage_tools.ImageSample object at 0x7f5c69df1f90>), ('7925', <ifimage_tools.ImageSample object at 0x7f5c69a179a0>), ('5863', <ifimage_tools.ImageSample object at 0x7f5c69df1510>), ('1112', <ifimage_tools.ImageSample object at 0x7f5c69df3dc0>), ('7739', <ifimage_tools.ImageSample object at 0x7f5c69df0c40>), ('5059', <ifimage_tools.ImageSample object at 0x7f5c69a158d0>), ('7113', <ifimage_tools.ImageSample object at 0x7f5c69a17550>), ('3569', <ifimage_tools.ImageSample object at 0x7f5c69a16e30>), ('7685', <ifimage_tools.ImageSample object at 0x7f5c69a17a00>), ('8224', <ifimage_tools.ImageSample object at 0x7f5e30bb5870>), ('4515', <ifimage_tools.ImageSample object at 0x7f5e30bb4fd0>), ('7870', <ifimage_tools.ImageSample object at 0x7f5c69a3b5b0>), ('6790', <ifimage_tools.ImageSample object at 0x7f5c69a147f0>), ('7962', <ifimage_tools.ImageSample object at 0x7f5c69dcbc40>), ('1111', <ifimage_tools.ImageSample object at 0x7f5ede6410f0>), ('8746', <ifimage_tools.ImageSample object at 0x7f5c69dcbfa0>), ('3532', <ifimage_tools.ImageSample object at 0x7f5c69b61390>), ('3999', <ifimage_tools.ImageSample object at 0x7f5c69b60370>), ('3527', <ifimage_tools.ImageSample object at 0x7f5c69a159f0>), ('7071', <ifimage_tools.ImageSample object at 0x7f5c6a2b3610>), ('5923', <ifimage_tools.ImageSample object at 0x7f5edefa35e0>), ('5789', <ifimage_tools.ImageSample object at 0x7f5c6a2b3220>), ('5794', <ifimage_tools.ImageSample object at 0x7f5c6a2b3640>), ('9755', <ifimage_tools.ImageSample object at 0x7f5c6a2b3310>), ('6020', <ifimage_tools.ImageSample object at 0x7f5c69b63670>), ('9170', <ifimage_tools.ImageSample object at 0x7f5c69b60340>), ('10015', <ifimage_tools.ImageSample object at 0x7f5c69b61690>), ('1116', <ifimage_tools.ImageSample object at 0x7f5c69a3a8f0>), ('4238', <ifimage_tools.ImageSample object at 0x7f5c69a3aad0>), ('1114', <ifimage_tools.ImageSample object at 0x7f5c69df30d0>), ('5795', <ifimage_tools.ImageSample object at 0x7f5c69df3880>), ('4548', <ifimage_tools.ImageSample object at 0x7f5c69df33a0>), ('8517', <ifimage_tools.ImageSample object at 0x7f5c69df2410>), ('9783', <ifimage_tools.ImageSample object at 0x7f5c69df2cb0>), ('1108', <ifimage_tools.ImageSample object at 0x7f5c69d93e20>), ('8310', <ifimage_tools.ImageSample object at 0x7f5c69d919f0>), ('9472', <ifimage_tools.ImageSample object at 0x7f5c69d90a00>), ('4736', <ifimage_tools.ImageSample object at 0x7f5c69d92410>), ('7319', <ifimage_tools.ImageSample object at 0x7f5c69d93ac0>), ('9866', <ifimage_tools.ImageSample object at 0x7f5c69d92230>), ('6212', <ifimage_tools.ImageSample object at 0x7f5c69df3730>), ('8942', <ifimage_tools.ImageSample object at 0x7f5c69df3400>), ('1113', <ifimage_tools.ImageSample object at 0x7f5c69df1030>), ('6748', <ifimage_tools.ImageSample object at 0x7f5c69a15660>), ('1115', <ifimage_tools.ImageSample object at 0x7f5c69a174c0>), ('5410', <ifimage_tools.ImageSample object at 0x7f5c69a155a0>), ('4201', <ifimage_tools.ImageSample object at 0x7f5c69a17f10>), ('6523', <ifimage_tools.ImageSample object at 0x7f5c69df3370>), ('6466', <ifimage_tools.ImageSample object at 0x7f5c69df3ca0>), ('4683', <ifimage_tools.ImageSample object at 0x7f5c69a16170>), ('4319', <ifimage_tools.ImageSample object at 0x7f5c69a14880>), ('1109', <ifimage_tools.ImageSample object at 0x7f5c69df2c50>)])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.samples.items()"]}, {"cell_type": "code", "execution_count": null, "id": "a3bb0952", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["cell seg:   1%|          | 1/100 [00:00<00:43,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5803] saved watershed_only_cyto → cell_masks/5803/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   2%|▏         | 2/100 [00:00<00:41,  2.33it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12781] saved watershed_only_cyto → cell_masks/12781/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   3%|▎         | 3/100 [00:01<00:43,  2.25it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12779] saved watershed_only_cyto → cell_masks/12779/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   4%|▍         | 4/100 [00:01<00:42,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12786] saved watershed_only_cyto → cell_masks/12786/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   5%|▌         | 5/100 [00:02<00:41,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[10594] saved watershed_only_cyto → cell_masks/10594/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   6%|▌         | 6/100 [00:02<00:40,  2.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8408jan22] saved watershed_only_cyto → cell_masks/8408jan22/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   7%|▋         | 7/100 [00:03<00:40,  2.30it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[15591] saved watershed_only_cyto → cell_masks/15591/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   8%|▊         | 8/100 [00:03<00:39,  2.33it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12793] saved watershed_only_cyto → cell_masks/12793/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:   9%|▉         | 9/100 [00:03<00:40,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[15972] saved watershed_only_cyto → cell_masks/15972/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  10%|█         | 10/100 [00:04<00:39,  2.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12794] saved watershed_only_cyto → cell_masks/12794/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  11%|█         | 11/100 [00:04<00:42,  2.10it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1120] saved watershed_only_cyto → cell_masks/1120/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  12%|█▏        | 12/100 [00:05<00:39,  2.22it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12795] saved watershed_only_cyto → cell_masks/12795/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  13%|█▎        | 13/100 [00:05<00:39,  2.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12792] saved watershed_only_cyto → cell_masks/12792/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  14%|█▍        | 14/100 [00:06<00:37,  2.28it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12787] saved watershed_only_cyto → cell_masks/12787/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  15%|█▌        | 15/100 [00:06<00:36,  2.35it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12780] saved watershed_only_cyto → cell_masks/12780/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  16%|█▌        | 16/100 [00:06<00:35,  2.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[2529] saved watershed_only_cyto → cell_masks/2529/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  17%|█▋        | 17/100 [00:07<00:36,  2.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8265] saved watershed_only_cyto → cell_masks/8265/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  18%|█▊        | 18/100 [00:07<00:35,  2.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[15973] saved watershed_only_cyto → cell_masks/15973/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  19%|█▉        | 19/100 [00:08<00:35,  2.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12798] saved watershed_only_cyto → cell_masks/12798/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  20%|██        | 20/100 [00:08<00:33,  2.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8407jan22] saved watershed_only_cyto → cell_masks/8407jan22/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  21%|██        | 21/100 [00:09<00:32,  2.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8406jan22] saved watershed_only_cyto → cell_masks/8406jan22/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  22%|██▏       | 22/100 [00:09<00:32,  2.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12799] saved watershed_only_cyto → cell_masks/12799/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  23%|██▎       | 23/100 [00:09<00:32,  2.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8405jan22] saved watershed_only_cyto → cell_masks/8405jan22/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  24%|██▍       | 24/100 [00:10<00:32,  2.35it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[15970] saved watershed_only_cyto → cell_masks/15970/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  25%|██▌       | 25/100 [00:10<00:33,  2.25it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12791] saved watershed_only_cyto → cell_masks/12791/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  26%|██▌       | 26/100 [00:11<00:32,  2.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12784] saved watershed_only_cyto → cell_masks/12784/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  27%|██▋       | 27/100 [00:11<00:31,  2.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[15968] saved watershed_only_cyto → cell_masks/15968/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  28%|██▊       | 28/100 [00:12<00:31,  2.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12789] saved watershed_only_cyto → cell_masks/12789/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  29%|██▉       | 29/100 [00:12<00:29,  2.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12783] saved watershed_only_cyto → cell_masks/12783/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  30%|███       | 30/100 [00:13<00:31,  2.23it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12796] saved watershed_only_cyto → cell_masks/12796/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  31%|███       | 31/100 [00:13<00:30,  2.24it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[15969] saved watershed_only_cyto → cell_masks/15969/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  32%|███▏      | 32/100 [00:14<00:31,  2.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12797] saved watershed_only_cyto → cell_masks/12797/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  33%|███▎      | 33/100 [00:14<00:31,  2.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12782] saved watershed_only_cyto → cell_masks/12782/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  34%|███▍      | 34/100 [00:14<00:30,  2.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[15971] saved watershed_only_cyto → cell_masks/15971/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  35%|███▌      | 35/100 [00:15<00:28,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12785] saved watershed_only_cyto → cell_masks/12785/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  36%|███▌      | 36/100 [00:15<00:28,  2.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12790] saved watershed_only_cyto → cell_masks/12790/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  37%|███▋      | 37/100 [00:16<00:27,  2.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[12788] saved watershed_only_cyto → cell_masks/12788/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  38%|███▊      | 38/100 [00:16<00:26,  2.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8409] saved watershed_only_cyto → cell_masks/8409/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  39%|███▉      | 39/100 [00:17<00:26,  2.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6390v2] saved watershed_only_cyto → cell_masks/6390v2/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  40%|████      | 40/100 [00:17<00:26,  2.30it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8550] saved watershed_only_cyto → cell_masks/8550/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  41%|████      | 41/100 [00:17<00:25,  2.28it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8917] saved watershed_only_cyto → cell_masks/8917/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  42%|████▏     | 42/100 [00:18<00:25,  2.30it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5191] saved watershed_only_cyto → cell_masks/5191/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  43%|████▎     | 43/100 [00:18<00:25,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6833] saved watershed_only_cyto → cell_masks/6833/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  44%|████▍     | 44/100 [00:19<00:25,  2.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[10166] saved watershed_only_cyto → cell_masks/10166/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  45%|████▌     | 45/100 [00:19<00:24,  2.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[9106] saved watershed_only_cyto → cell_masks/9106/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  46%|████▌     | 46/100 [00:20<00:25,  2.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4642] saved watershed_only_cyto → cell_masks/4642/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  47%|████▋     | 47/100 [00:20<00:24,  2.15it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5792] saved watershed_only_cyto → cell_masks/5792/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  48%|████▊     | 48/100 [00:21<00:23,  2.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[10061] saved watershed_only_cyto → cell_masks/10061/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  49%|████▉     | 49/100 [00:21<00:24,  2.11it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1110] saved watershed_only_cyto → cell_masks/1110/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  50%|█████     | 50/100 [00:22<00:22,  2.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7925] saved watershed_only_cyto → cell_masks/7925/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  51%|█████     | 51/100 [00:22<00:21,  2.23it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5863] saved watershed_only_cyto → cell_masks/5863/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  52%|█████▏    | 52/100 [00:23<00:22,  2.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1112] saved watershed_only_cyto → cell_masks/1112/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  53%|█████▎    | 53/100 [00:23<00:21,  2.22it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7739] saved watershed_only_cyto → cell_masks/7739/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  54%|█████▍    | 54/100 [00:23<00:21,  2.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5059] saved watershed_only_cyto → cell_masks/5059/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  55%|█████▌    | 55/100 [00:24<00:21,  2.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7113] saved watershed_only_cyto → cell_masks/7113/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  56%|█████▌    | 56/100 [00:24<00:20,  2.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[3569] saved watershed_only_cyto → cell_masks/3569/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  57%|█████▋    | 57/100 [00:25<00:19,  2.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7685] saved watershed_only_cyto → cell_masks/7685/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  58%|█████▊    | 58/100 [00:25<00:19,  2.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8224] saved watershed_only_cyto → cell_masks/8224/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  59%|█████▉    | 59/100 [00:26<00:19,  2.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4515] saved watershed_only_cyto → cell_masks/4515/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  60%|██████    | 60/100 [00:26<00:18,  2.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7870] saved watershed_only_cyto → cell_masks/7870/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  61%|██████    | 61/100 [00:27<00:17,  2.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6790] saved watershed_only_cyto → cell_masks/6790/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  62%|██████▏   | 62/100 [00:27<00:17,  2.23it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7962] saved watershed_only_cyto → cell_masks/7962/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  63%|██████▎   | 63/100 [00:28<00:17,  2.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1111] saved watershed_only_cyto → cell_masks/1111/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  64%|██████▍   | 64/100 [00:28<00:16,  2.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8746] saved watershed_only_cyto → cell_masks/8746/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  65%|██████▌   | 65/100 [00:28<00:15,  2.23it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[3532] saved watershed_only_cyto → cell_masks/3532/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  66%|██████▌   | 66/100 [00:29<00:15,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[3999] saved watershed_only_cyto → cell_masks/3999/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  67%|██████▋   | 67/100 [00:29<00:14,  2.23it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[3527] saved watershed_only_cyto → cell_masks/3527/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  68%|██████▊   | 68/100 [00:30<00:14,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7071] saved watershed_only_cyto → cell_masks/7071/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  69%|██████▉   | 69/100 [00:30<00:13,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5923] saved watershed_only_cyto → cell_masks/5923/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  70%|███████   | 70/100 [00:31<00:13,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5789] saved watershed_only_cyto → cell_masks/5789/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  71%|███████   | 71/100 [00:31<00:12,  2.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5794] saved watershed_only_cyto → cell_masks/5794/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  72%|███████▏  | 72/100 [00:32<00:13,  2.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[9755] saved watershed_only_cyto → cell_masks/9755/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  73%|███████▎  | 73/100 [00:32<00:13,  2.01it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6020] saved watershed_only_cyto → cell_masks/6020/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  74%|███████▍  | 74/100 [00:33<00:12,  2.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[9170] saved watershed_only_cyto → cell_masks/9170/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  75%|███████▌  | 75/100 [00:33<00:11,  2.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[10015] saved watershed_only_cyto → cell_masks/10015/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  76%|███████▌  | 76/100 [00:34<00:11,  2.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1116] saved watershed_only_cyto → cell_masks/1116/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  77%|███████▋  | 77/100 [00:34<00:10,  2.10it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4238] saved watershed_only_cyto → cell_masks/4238/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  78%|███████▊  | 78/100 [00:35<00:11,  1.93it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1114] saved watershed_only_cyto → cell_masks/1114/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  79%|███████▉  | 79/100 [00:35<00:10,  2.05it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5795] saved watershed_only_cyto → cell_masks/5795/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  80%|████████  | 80/100 [00:36<00:09,  2.06it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4548] saved watershed_only_cyto → cell_masks/4548/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  81%|████████  | 81/100 [00:36<00:08,  2.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8517] saved watershed_only_cyto → cell_masks/8517/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  82%|████████▏ | 82/100 [00:36<00:08,  2.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[9783] saved watershed_only_cyto → cell_masks/9783/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  83%|████████▎ | 83/100 [00:37<00:07,  2.15it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1108] saved watershed_only_cyto → cell_masks/1108/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  84%|████████▍ | 84/100 [00:37<00:07,  2.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8310] saved watershed_only_cyto → cell_masks/8310/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  85%|████████▌ | 85/100 [00:38<00:06,  2.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[9472] saved watershed_only_cyto → cell_masks/9472/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  86%|████████▌ | 86/100 [00:38<00:06,  2.25it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4736] saved watershed_only_cyto → cell_masks/4736/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  87%|████████▋ | 87/100 [00:39<00:05,  2.28it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[7319] saved watershed_only_cyto → cell_masks/7319/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  88%|████████▊ | 88/100 [00:39<00:05,  2.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[9866] saved watershed_only_cyto → cell_masks/9866/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  89%|████████▉ | 89/100 [00:40<00:05,  2.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6212] saved watershed_only_cyto → cell_masks/6212/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  90%|█████████ | 90/100 [00:40<00:04,  2.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[8942] saved watershed_only_cyto → cell_masks/8942/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  91%|█████████ | 91/100 [00:41<00:04,  2.07it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1113] saved watershed_only_cyto → cell_masks/1113/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  92%|█████████▏| 92/100 [00:41<00:03,  2.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6748] saved watershed_only_cyto → cell_masks/6748/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  93%|█████████▎| 93/100 [00:42<00:03,  2.05it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1115] saved watershed_only_cyto → cell_masks/1115/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  94%|█████████▍| 94/100 [00:42<00:02,  2.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[5410] saved watershed_only_cyto → cell_masks/5410/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  95%|█████████▌| 95/100 [00:42<00:02,  2.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4201] saved watershed_only_cyto → cell_masks/4201/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  96%|█████████▌| 96/100 [00:43<00:01,  2.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6523] saved watershed_only_cyto → cell_masks/6523/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  97%|█████████▋| 97/100 [00:43<00:01,  2.28it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[6466] saved watershed_only_cyto → cell_masks/6466/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  98%|█████████▊| 98/100 [00:44<00:00,  2.22it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4683] saved watershed_only_cyto → cell_masks/4683/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg:  99%|█████████▉| 99/100 [00:44<00:00,  2.24it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[4319] saved watershed_only_cyto → cell_masks/4319/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["cell seg: 100%|██████████| 100/100 [00:45<00:00,  2.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[1109] saved watershed_only_cyto → cell_masks/1109/watershed_only_cyto.npy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["methods = [\"cellpose\",\"cellpose2chan\"]\n", "\n", "for sample_id, sample in tqdm(dataset.samples.items(), desc=\"cell seg\"):\n", "    # 跑细胞质阳性分割\n", "    sample.get_positive_cyto_pipline(methods=methods)\n", "\n", "    # mkdir cell_masks/<sample_id>/\n", "    sample_dir = os.path.join(output_root, sample_id)\n", "    os.makedirs(sample_dir, exist_ok=True)\n", "\n", "    # 保存每种算法的 mask\n", "    for m in methods:\n", "        mask = sample.cyto_positive_masks.get(m)\n", "        if mask is None:\n", "            print(\"失败！\")\n", "            continue                      # 方法失败/未跑\n", "        out_path = os.path.join(sample_dir, f\"{m}.npy\")\n", "        np.save(out_path, mask.astype(np.uint32))\n", "        print(f\"[{sample_id}] saved {m} → {out_path}\")\n"]}], "metadata": {"kernelspec": {"display_name": "ifimage", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}