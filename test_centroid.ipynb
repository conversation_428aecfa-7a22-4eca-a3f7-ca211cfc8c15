{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-17T08:36:54.304287Z", "start_time": "2025-03-17T08:36:54.286051Z"}}, "source": ["import importlib\n", "import ifimage_tools\n", "importlib.reload(ifimage_tools)\n", "import pandas as pd\n", "import warnings"], "outputs": [], "execution_count": 4}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "32e0dd540599b3fd"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T08:37:03.042082Z", "start_time": "2025-03-17T08:36:54.397786Z"}}, "cell_type": "code", "source": ["\n", "\n", "\n", "\n", "import os\n", "import numpy as np\n", "from stardist.matching import matching\n", "image_dir = \"Reorgnized Ground Truth\"\n", "masks_dir = \"Reorgnized Ground Truth/mask\"\n", "old_version_sample_ids=[\"6390\",\"8408\",\"8406\",\"8405v2\",\"8405\",\"8407\"]\n", "dataset = ifimage_tools.IfImageDataset(image_dir, masks_dir, {})\n", "dataset.load_data()\n", "old_version_sample_ids = [\"6390\", \"8408\", \"8406\", \"8405v2\", \"8405\", \"8407\"]\n", "\n", "for sample_id in old_version_sample_ids:\n", "    if sample_id in dataset.samples:\n", "        del dataset.samples[sample_id]\n", "METHODS = [\"cyto3\", \"watershed\", \"cell_expansion\"]"], "id": "f543055c3b155e71", "outputs": [], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T08:37:05.173396Z", "start_time": "2025-03-17T08:37:03.068283Z"}}, "cell_type": "code", "source": ["import joblib\n", "with open(\"dataset_after_pipeline.joblib\", \"rb\") as f:\n", "    dataset = joblib.load(f)"], "id": "838c56f61d19793a", "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T08:37:22.937731Z", "start_time": "2025-03-17T08:37:22.930191Z"}}, "cell_type": "code", "source": "randomsample=dataset.get_random_sample()", "id": "b1f00ad5cc739db1", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample ID: 12795, Cell Type: olig2\n"]}], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T08:58:59.143742Z", "start_time": "2025-03-17T08:58:58.838591Z"}}, "cell_type": "code", "source": ["from ifimage_tools import *\n", "gt=randomsample.cellbodies_multimask\n", "cp=randomsample.cyto_positive_masks[\"cell_expansion\"]\n", "CentroidAnalyzer.plot_centroids_with_cyto(gt,cp,linkage=True)"], "id": "1860f75efaad0c68", "outputs": [{"data": {"text/plain": ["<Figure size 800x800 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 12}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "dataset.# Relations", "id": "7fc279a6df4457b8"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T09:21:51.716913Z", "start_time": "2025-03-17T09:21:51.706418Z"}}, "cell_type": "code", "source": ["for sample_id, sample in dataset.samples.items():\n", "    gt_mask = sample.cellbodies_multimask\n", "    if gt_mask is None:\n", "        print(sample.sample_id)\n", "    # for method in METHODS:\n", "    #     if method not in sample.cyto_positive_masks:\n", "    #         print(f\"Sample {sample_id} lacks prediction for method {method}; skipping...\")\n", "    #         continue\n", "    #     pred_mask_source = sample.cyto_positive_masks[method]\n", "    #     try:\n", "    #         if isinstance(pred_mask_source, np.ndarray):\n", "    #             pred_mask = pred_mask_source\n", "    #         else:\n", "    #             if not os.path.exists(pred_mask_source):\n", "    #                 print(f\"Prediction file does not exist: {pred_mask_source} (sample {sample_id}, method {method}); skipping...\")\n", "    #                 continue\n", "    #             pred_mask = np.load(pred_mask_source)\n", "    #     except Exception as e:\n", "    #         print(f\"Error loading prediction mask (sample {sample_id}, method {method}): {e}; skipping...\")\n", "    #         continue"], "id": "98ecc45598a8a8e8", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6390v2\n", "8408jan22\n", "8406jan22\n", "8405v2\n", "8405jan22\n", "8407jan22\n"]}], "execution_count": 21}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "1a6bd7f23aa81813"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}